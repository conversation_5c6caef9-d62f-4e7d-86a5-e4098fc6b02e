import { Agent } from './types';

export interface Problem {
  id: string;
  description: string;
  complexity: number;
  domain: string;
  constraints: string[];
  deadline?: number;
}

export interface Solution {
  id: string;
  problemId: string;
  approach: string;
  confidence: number;
  estimatedEffort: number;
  proposedBy: string;
  votes: Vote[];
  consensus: number;
}

export interface Vote {
  agentId: string;
  support: number; // -1 to 1
  reasoning: string;
  expertise: number;
  timestamp: number;
}

export interface Consensus {
  problemId: string;
  agreedSolution: Solution;
  consensusLevel: number;
  participatingAgents: string[];
  dissenting: Agent[];
  timeToConsensus: number;
}

export interface Opinion {
  agentId: string;
  position: string;
  confidence: number;
  evidence: string[];
  bias: number;
}

export interface ValidationResult {
  isValid: boolean;
  confidence: number;
  validators: string[];
  issues: string[];
  recommendations: string[];
}

/**
 * Multi-Agent Consensus Engine
 * Implements democratic decision making and conflict resolution
 */
export class ConsensusEngine {
  private static instance: ConsensusEngine;
  private consensusHistory: Consensus[] = [];
  private agentExpertise: Map<string, Map<string, number>> = new Map();

  public static getInstance(): ConsensusEngine {
    if (!ConsensusEngine.instance) {
      ConsensusEngine.instance = new ConsensusEngine();
    }
    return ConsensusEngine.instance;
  }

  /**
   * Conduct voting among agents for a solution
   */
  public async voteOnSolution(agents: Agent[], problem: Problem): Promise<Solution> {
    try {
      // Generate potential solutions from agents
      const potentialSolutions = await this.generateSolutions(agents, problem);
      
      // Conduct voting rounds
      const votingResults = await this.conductVoting(agents, potentialSolutions);
      
      // Calculate consensus
      const bestSolution = this.calculateBestSolution(votingResults);
      
      // Record consensus
      this.recordConsensus(problem, bestSolution, agents);
      
      return bestSolution;
    } catch (error) {
      console.error('Error in voting process:', error);
      return this.getDefaultSolution(problem);
    }
  }

  /**
   * Resolve conflicts between different agent opinions
   */
  public resolveConflicts(conflictingResults: any[]): any {
    try {
      if (conflictingResults.length === 0) {
        return { resolution: null, confidence: 0 };
      }

      // Analyze conflicts
      const conflictAnalysis = this.analyzeConflicts(conflictingResults);
      
      // Apply resolution strategies
      const resolution = this.applyResolutionStrategy(conflictAnalysis);
      
      // Validate resolution
      const validation = this.validateResolution(resolution, conflictingResults);
      
      return {
        resolution: resolution.result,
        confidence: validation.confidence,
        method: resolution.method,
        participants: conflictingResults.map(r => r.agentId),
        resolutionTime: Date.now()
      };
    } catch (error) {
      console.error('Error resolving conflicts:', error);
      return { resolution: null, confidence: 0, method: 'fallback' };
    }
  }

  /**
   * Build consensus from multiple opinions
   */
  public buildConsensus(opinions: Opinion[]): Consensus {
    try {
      if (opinions.length === 0) {
        return this.getEmptyConsensus();
      }

      // Weight opinions by expertise and confidence
      const weightedOpinions = this.weightOpinions(opinions);
      
      // Find common ground
      const commonGround = this.findCommonGround(weightedOpinions);
      
      // Build consensus solution
      const consensusSolution = this.buildConsensusSolution(commonGround);
      
      // Calculate consensus level
      const consensusLevel = this.calculateConsensusLevel(opinions, consensusSolution);
      
      const consensus: Consensus = {
        problemId: 'consensus_' + Date.now(),
        agreedSolution: consensusSolution,
        consensusLevel,
        participatingAgents: opinions.map(o => o.agentId),
        dissenting: this.identifyDissentingAgents(opinions, consensusSolution),
        timeToConsensus: this.estimateConsensusTime(opinions)
      };

      this.consensusHistory.push(consensus);
      return consensus;
    } catch (error) {
      console.error('Error building consensus:', error);
      return this.getEmptyConsensus();
    }
  }

  /**
   * Validate output through committee review
   */
  public validateByCommittee(output: any): ValidationResult {
    try {
      // Select validation committee
      const committee = this.selectValidationCommittee(output);
      
      // Conduct validation
      const validationResults = this.conductValidation(committee, output);
      
      // Aggregate results
      const aggregatedResult = this.aggregateValidationResults(validationResults);
      
      return {
        isValid: aggregatedResult.isValid,
        confidence: aggregatedResult.confidence,
        validators: committee.map(agent => agent.id),
        issues: aggregatedResult.issues,
        recommendations: aggregatedResult.recommendations
      };
    } catch (error) {
      console.error('Error in committee validation:', error);
      return {
        isValid: false,
        confidence: 0,
        validators: [],
        issues: ['validation_error'],
        recommendations: ['retry_validation']
      };
    }
  }

  /**
   * Get consensus metrics and statistics
   */
  public getConsensusMetrics(): any {
    try {
      const totalConsensus = this.consensusHistory.length;
      const avgConsensusLevel = totalConsensus > 0 
        ? this.consensusHistory.reduce((sum, c) => sum + c.consensusLevel, 0) / totalConsensus
        : 0;
      
      const avgTimeToConsensus = totalConsensus > 0
        ? this.consensusHistory.reduce((sum, c) => sum + c.timeToConsensus, 0) / totalConsensus
        : 0;

      return {
        totalDecisions: totalConsensus,
        averageConsensusLevel: avgConsensusLevel,
        averageTimeToConsensus: avgTimeToConsensus,
        successRate: this.calculateSuccessRate(),
        conflictResolutionRate: this.calculateConflictResolutionRate(),
        participationRate: this.calculateParticipationRate()
      };
    } catch (error) {
      console.error('Error getting consensus metrics:', error);
      return {
        totalDecisions: 0,
        averageConsensusLevel: 0,
        averageTimeToConsensus: 0,
        successRate: 0,
        conflictResolutionRate: 0,
        participationRate: 0
      };
    }
  }

  private async generateSolutions(agents: Agent[], problem: Problem): Promise<Solution[]> {
    const solutions: Solution[] = [];
    
    for (const agent of agents) {
      // Mock solution generation - in production, call actual agent
      const solution: Solution = {
        id: `sol_${agent.id}_${Date.now()}`,
        problemId: problem.id,
        approach: `approach_by_${agent.id}`,
        confidence: Math.random() * 0.4 + 0.6,
        estimatedEffort: Math.random() * 100 + 50,
        proposedBy: agent.id,
        votes: [],
        consensus: 0
      };
      
      solutions.push(solution);
    }
    
    return solutions;
  }

  private async conductVoting(agents: Agent[], solutions: Solution[]): Promise<Solution[]> {
    for (const solution of solutions) {
      for (const agent of agents) {
        if (agent.id !== solution.proposedBy) {
          // Mock voting - in production, get actual agent vote
          const vote: Vote = {
            agentId: agent.id,
            support: Math.random() * 2 - 1, // -1 to 1
            reasoning: `reasoning_from_${agent.id}`,
            expertise: this.getAgentExpertise(agent.id, 'general'),
            timestamp: Date.now()
          };
          
          solution.votes.push(vote);
        }
      }
      
      // Calculate consensus for this solution
      solution.consensus = this.calculateSolutionConsensus(solution);
    }
    
    return solutions;
  }

  private calculateBestSolution(solutions: Solution[]): Solution {
    return solutions.reduce((best, current) => 
      current.consensus > best.consensus ? current : best
    );
  }

  private calculateSolutionConsensus(solution: Solution): number {
    if (solution.votes.length === 0) return 0;
    
    const weightedSupport = solution.votes.reduce((sum, vote) => 
      sum + (vote.support * vote.expertise), 0
    );
    
    const totalWeight = solution.votes.reduce((sum, vote) => sum + vote.expertise, 0);
    
    return totalWeight > 0 ? weightedSupport / totalWeight : 0;
  }

  private recordConsensus(problem: Problem, solution: Solution, agents: Agent[]): void {
    const consensus: Consensus = {
      problemId: problem.id,
      agreedSolution: solution,
      consensusLevel: solution.consensus,
      participatingAgents: agents.map(a => a.id),
      dissenting: agents.filter(a => {
        const vote = solution.votes.find(v => v.agentId === a.id);
        return vote && vote.support < 0;
      }),
      timeToConsensus: 1000 // Mock time
    };
    
    this.consensusHistory.push(consensus);
  }

  private analyzeConflicts(conflictingResults: any[]): any {
    const conflicts = {
      types: [] as string[],
      severity: 0,
      participants: conflictingResults.map(r => r.agentId),
      disagreementPoints: [] as string[]
    };
    
    // Analyze types of conflicts
    if (conflictingResults.length > 1) {
      conflicts.types.push('opinion_divergence');
      conflicts.severity = this.calculateConflictSeverity(conflictingResults);
    }
    
    return conflicts;
  }

  private applyResolutionStrategy(conflictAnalysis: any): any {
    // Choose resolution strategy based on conflict type
    let strategy = 'weighted_average';
    
    if (conflictAnalysis.severity > 0.8) {
      strategy = 'expert_arbitration';
    } else if (conflictAnalysis.types.includes('opinion_divergence')) {
      strategy = 'consensus_building';
    }
    
    return {
      method: strategy,
      result: this.executeResolutionStrategy(strategy, conflictAnalysis)
    };
  }

  private executeResolutionStrategy(strategy: string, _analysis: any): any {
    switch (strategy) {
      case 'weighted_average':
        return { resolution: 'averaged_solution', confidence: 0.7 };
      case 'expert_arbitration':
        return { resolution: 'expert_decision', confidence: 0.9 };
      case 'consensus_building':
        return { resolution: 'consensus_solution', confidence: 0.8 };
      default:
        return { resolution: 'default_solution', confidence: 0.5 };
    }
  }

  private validateResolution(_resolution: any, _originalResults: any[]): any {
    // Validate that resolution addresses original conflicts
    const confidence = Math.random() * 0.3 + 0.7; // Mock validation
    
    return {
      confidence,
      isValid: confidence > 0.6,
      issues: confidence < 0.8 ? ['low_confidence'] : []
    };
  }

  private weightOpinions(opinions: Opinion[]): Opinion[] {
    return opinions.map(opinion => ({
      ...opinion,
      confidence: opinion.confidence * (1 - opinion.bias) // Adjust for bias
    }));
  }

  private findCommonGround(_opinions: Opinion[]): any {
    // Find areas of agreement
    const commonElements = {
      sharedPositions: [] as string[],
      agreementLevel: 0,
      convergencePoints: [] as string[]
    };
    
    // Mock common ground finding
    commonElements.sharedPositions.push('basic_approach');
    commonElements.agreementLevel = 0.75;
    
    return commonElements;
  }

  private buildConsensusSolution(commonGround: any): Solution {
    return {
      id: 'consensus_' + Date.now(),
      problemId: 'consensus_problem',
      approach: 'consensus_approach',
      confidence: commonGround.agreementLevel,
      estimatedEffort: 100,
      proposedBy: 'consensus_engine',
      votes: [],
      consensus: commonGround.agreementLevel
    };
  }

  private calculateConsensusLevel(opinions: Opinion[], solution: Solution): number {
    if (opinions.length === 0) return 0;
    
    // Calculate how well the solution represents all opinions
    const alignment = opinions.reduce((sum, opinion) => {
      const similarity = this.calculateSimilarity(opinion.position, solution.approach);
      return sum + (similarity * opinion.confidence);
    }, 0);
    
    return alignment / opinions.length;
  }

  private calculateSimilarity(_position: string, _approach: string): number {
    // Mock similarity calculation
    return Math.random() * 0.4 + 0.6;
  }

  private identifyDissentingAgents(_opinions: Opinion[], _solution: Solution): Agent[] {
    // Mock dissenting agent identification
    return [];
  }

  private estimateConsensusTime(opinions: Opinion[]): number {
    // Estimate time based on opinion diversity
    const diversity = this.calculateOpinionDiversity(opinions);
    return diversity * 1000 + 500; // Base time + diversity factor
  }

  private calculateOpinionDiversity(opinions: Opinion[]): number {
    if (opinions.length <= 1) return 0;
    
    // Calculate variance in positions
    const positions = opinions.map(o => o.confidence);
    const mean = positions.reduce((sum, p) => sum + p, 0) / positions.length;
    const variance = positions.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / positions.length;
    
    return Math.sqrt(variance);
  }

  private selectValidationCommittee(_output: any): Agent[] {
    // Mock committee selection - create proper BaseAgent objects
    return [
      {
        id: 'validator_1',
        name: 'Validator Agent 1',
        type: 'validator' as const,
        description: 'Primary validation agent',
        capabilities: ['validation'],
        status: 'idle' as const,
        config: {
          model: 'sonnet' as const,
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 30000,
          retry_count: 3,
          parallel_execution: false,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: [],
          mcp_servers: []
        },
        metadata: {
          author: 'system',
          version: '1.0.0',
          tags: ['validation'],
          category: 'quality-assurance',
          difficulty: 'intermediate' as const,
          estimated_duration: 300,
          success_rate: 0.9,
          usage_count: 0,
          last_used: new Date().toISOString(),
          performance_metrics: {
            average_execution_time: 250,
            success_rate: 0.9,
            error_rate: 0.1,
            resource_usage: {
              cpu_usage: 0.3,
              memory_usage: 0.2,
              token_usage: 1000,
              api_calls: 5
            },
            quality_score: 0.85
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'validator_2',
        name: 'Validator Agent 2',
        type: 'validator' as const,
        description: 'Secondary validation agent',
        capabilities: ['validation'],
        status: 'idle' as const,
        config: {
          model: 'sonnet' as const,
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 30000,
          retry_count: 3,
          parallel_execution: false,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: [],
          mcp_servers: []
        },
        metadata: {
          author: 'system',
          version: '1.0.0',
          tags: ['validation'],
          category: 'quality-assurance',
          difficulty: 'intermediate' as const,
          estimated_duration: 300,
          success_rate: 0.85,
          usage_count: 0,
          last_used: new Date().toISOString(),
          performance_metrics: {
            average_execution_time: 280,
            success_rate: 0.85,
            error_rate: 0.15,
            resource_usage: {
              cpu_usage: 0.3,
              memory_usage: 0.2,
              token_usage: 1000,
              api_calls: 5
            },
            quality_score: 0.8
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private conductValidation(committee: Agent[], _output: any): any[] {
    return committee.map(agent => ({
      agentId: agent.id,
      isValid: Math.random() > 0.2,
      confidence: Math.random() * 0.3 + 0.7,
      issues: Math.random() > 0.7 ? ['minor_issue'] : [],
      recommendations: ['improve_clarity']
    }));
  }

  private aggregateValidationResults(results: any[]): any {
    const validCount = results.filter(r => r.isValid).length;
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
    const allIssues = results.flatMap(r => r.issues);
    const allRecommendations = results.flatMap(r => r.recommendations);
    
    return {
      isValid: validCount / results.length > 0.5,
      confidence: avgConfidence,
      issues: [...new Set(allIssues)],
      recommendations: [...new Set(allRecommendations)]
    };
  }

  private getAgentExpertise(agentId: string, domain: string): number {
    const agentExpertise = this.agentExpertise.get(agentId);
    return agentExpertise?.get(domain) || 0.5;
  }

  private calculateConflictSeverity(_results: any[]): number {
    // Calculate how severe the conflict is
    return Math.random() * 0.5 + 0.3; // Mock calculation
  }

  private calculateSuccessRate(): number {
    if (this.consensusHistory.length === 0) return 0;
    const successful = this.consensusHistory.filter(c => c.consensusLevel > 0.7).length;
    return successful / this.consensusHistory.length;
  }

  private calculateConflictResolutionRate(): number {
    // Mock calculation
    return 0.85;
  }

  private calculateParticipationRate(): number {
    // Mock calculation
    return 0.92;
  }

  private getDefaultSolution(problem: Problem): Solution {
    return {
      id: 'default_' + Date.now(),
      problemId: problem.id,
      approach: 'default_approach',
      confidence: 0.5,
      estimatedEffort: 100,
      proposedBy: 'system',
      votes: [],
      consensus: 0.5
    };
  }

  private getEmptyConsensus(): Consensus {
    return {
      problemId: 'empty',
      agreedSolution: this.getDefaultSolution({ id: 'empty', description: '', complexity: 0, domain: '', constraints: [] }),
      consensusLevel: 0,
      participatingAgents: [],
      dissenting: [],
      timeToConsensus: 0
    };
  }
}
