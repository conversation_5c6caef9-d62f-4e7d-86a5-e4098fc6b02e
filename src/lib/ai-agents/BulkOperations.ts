import { BulkOperation, BulkTarget, BulkResult } from './types';
import { TaskMaster } from './TaskMaster';

export class BulkOperationsManager {
  private static instance: BulkOperationsManager;
  private taskMaster: TaskMaster;

  public static getInstance(): BulkOperationsManager {
    if (!BulkOperationsManager.instance) {
      BulkOperationsManager.instance = new BulkOperationsManager();
    }
    return BulkOperationsManager.instance;
  }

  constructor() {
    this.taskMaster = TaskMaster.getInstance();
  }

  // Predefined bulk operations
  async createCodeReviewBatch(targets: BulkTarget[]): Promise<BulkOperation> {
    return {
      id: `bulk-review-${Date.now()}`,
      name: 'Code Review Batch',
      type: 'code_review_batch',
      targets,
      agent_template: {
        type: 'code-reviewer',
        config: {
          model: 'sonnet',
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 300,
          retry_count: 3,
          parallel_execution: true,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: ['static_analysis', 'security_scanner'],
          mcp_servers: ['filesystem', 'git']
        }
      },
      batch_size: 10,
      parallel_limit: 3,
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };
  }

  async createRefactorBatch(targets: BulkTarget[]): Promise<BulkOperation> {
    return {
      id: `bulk-refactor-${Date.now()}`,
      name: 'Refactoring Batch',
      type: 'refactor_batch',
      targets,
      agent_template: {
        type: 'refactorer',
        config: {
          model: 'sonnet',
          temperature: 0.1,
          max_tokens: 4000,
          timeout: 400,
          retry_count: 3,
          parallel_execution: true,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: ['ast_parser', 'pattern_detector'],
          mcp_servers: ['filesystem', 'git']
        }
      },
      batch_size: 5,
      parallel_limit: 2,
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };
  }

  async createTestGenerationBatch(targets: BulkTarget[]): Promise<BulkOperation> {
    return {
      id: `bulk-test-gen-${Date.now()}`,
      name: 'Test Generation Batch',
      type: 'test_generation',
      targets,
      agent_template: {
        type: 'tester',
        config: {
          model: 'sonnet',
          temperature: 0.2,
          max_tokens: 3000,
          timeout: 300,
          retry_count: 3,
          parallel_execution: true,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: ['test_generator', 'coverage_analyzer'],
          mcp_servers: ['filesystem']
        }
      },
      batch_size: 8,
      parallel_limit: 4,
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };
  }

  async createDocumentationBatch(targets: BulkTarget[]): Promise<BulkOperation> {
    return {
      id: `bulk-docs-${Date.now()}`,
      name: 'Documentation Batch',
      type: 'documentation_batch',
      targets,
      agent_template: {
        type: 'documenter',
        config: {
          model: 'sonnet',
          temperature: 0.3,
          max_tokens: 4000,
          timeout: 300,
          retry_count: 2,
          parallel_execution: true,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: ['doc_generator', 'markdown_formatter'],
          mcp_servers: ['filesystem']
        }
      },
      batch_size: 15,
      parallel_limit: 5,
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };
  }

  async createSecurityScanBatch(targets: BulkTarget[]): Promise<BulkOperation> {
    return {
      id: `bulk-security-${Date.now()}`,
      name: 'Security Scan Batch',
      type: 'security_scan_batch',
      targets,
      agent_template: {
        type: 'security-scanner',
        config: {
          model: 'sonnet',
          temperature: 0.1,
          max_tokens: 3000,
          timeout: 200,
          retry_count: 3,
          parallel_execution: true,
          dependencies: [],
          triggers: [],
          outputs: [],
          custom_prompts: {},
          tools: ['security_scanner', 'vulnerability_detector'],
          mcp_servers: ['filesystem']
        }
      },
      batch_size: 20,
      parallel_limit: 6,
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };
  }

  // Utility methods for creating targets
  createFileTargets(filePaths: string[]): BulkTarget[] {
    return filePaths.map((path, index) => ({
      id: `file-${index}`,
      type: 'file',
      path,
      priority: 1
    }));
  }

  createDirectoryTargets(dirPaths: string[]): BulkTarget[] {
    return dirPaths.map((path, index) => ({
      id: `dir-${index}`,
      type: 'directory',
      path,
      priority: 1
    }));
  }

  createProjectTargets(projectPaths: string[]): BulkTarget[] {
    return projectPaths.map((path, index) => ({
      id: `project-${index}`,
      type: 'project',
      path,
      priority: 1
    }));
  }

  // Execution methods
  async executeBulkOperation(operation: BulkOperation): Promise<void> {
    return this.taskMaster.executeBulkOperation(operation.id);
  }

  // Progress tracking
  getBulkOperationProgress(operationId: string): number {
    const operations = this.taskMaster.getRunningBulkOperations();
    const operation = operations.find(op => op.id === operationId);
    return operation?.progress.percentage || 0;
  }

  getBulkOperationResults(operationId: string): BulkResult[] {
    const operations = this.taskMaster.getRunningBulkOperations();
    const operation = operations.find(op => op.id === operationId);
    return operation?.results || [];
  }

  // Predefined workflows for common bulk operations
  async createFullProjectAnalysis(projectPath: string): Promise<BulkOperation[]> {
    const targets = this.createProjectTargets([projectPath]);
    
    return [
      await this.createSecurityScanBatch(targets),
      await this.createCodeReviewBatch(targets),
      await this.createTestGenerationBatch(targets),
      await this.createDocumentationBatch(targets)
    ];
  }

  async createCodeQualityBatch(filePaths: string[]): Promise<BulkOperation[]> {
    const targets = this.createFileTargets(filePaths);
    
    return [
      await this.createCodeReviewBatch(targets),
      await this.createRefactorBatch(targets)
    ];
  }

  // Advanced filtering and targeting
  filterTargetsByExtension(targets: BulkTarget[], extensions: string[]): BulkTarget[] {
    return targets.filter(target => {
      const ext = target.path.split('.').pop()?.toLowerCase();
      return ext && extensions.includes(ext);
    });
  }

  filterTargetsBySize(targets: BulkTarget[]): BulkTarget[] {
    // This would need file system access to check actual sizes
    return targets.filter(() => {
      // Placeholder logic - would need actual file size checking
      return true;
    });
  }

  prioritizeTargets(targets: BulkTarget[], priorityRules: Record<string, number>): BulkTarget[] {
    return targets.map(target => {
      const ext = target.path.split('.').pop()?.toLowerCase();
      const priority = ext && priorityRules[ext] ? priorityRules[ext] : target.priority || 1;
      return { ...target, priority };
    }).sort((a, b) => (b.priority || 1) - (a.priority || 1));
  }
}