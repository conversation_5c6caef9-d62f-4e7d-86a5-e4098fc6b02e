import { Task, TaskPriority } from './types';
import { AIAgentLibrary } from './AgentLibrary';
import { EventEmitter } from '../utils/EventEmitter';

export interface ParallelExecutionConfig {
  maxConcurrentAgents: number;
  maxConcurrentTasksPerAgent: number;
  resourceLimits: {
    maxMemoryUsage: number; // MB
    maxCpuUsage: number; // percentage
    maxTokenUsage: number;
    maxApiCalls: number;
  };
  scalingPolicy: {
    enabled: boolean;
    minAgents: number;
    maxAgents: number;
    scaleUpThreshold: number; // queue length
    scaleDownThreshold: number;
    cooldownPeriod: number; // seconds
  };
  loadBalancing: {
    strategy: 'round_robin' | 'least_loaded' | 'capability_based' | 'priority_weighted';
    enableStickySessions: boolean;
    healthCheckInterval: number;
  };
  failureHandling: {
    maxRetries: number;
    retryDelay: number;
    circuitBreakerThreshold: number;
    fallbackStrategy: 'redistribute' | 'queue' | 'fail_fast';
  };
}

export interface AgentPool {
  id: string;
  agentType: string;
  instances: AgentInstance[];
  maxInstances: number;
  currentLoad: number;
  averageResponseTime: number;
  successRate: number;
  lastScaleAction: Date;
}

export interface AgentInstance {
  id: string;
  agentId: string;
  status: 'idle' | 'busy' | 'overloaded' | 'failed' | 'scaling';
  currentTasks: string[];
  resourceUsage: {
    memory: number;
    cpu: number;
    tokens: number;
    apiCalls: number;
  };
  performance: {
    tasksCompleted: number;
    averageExecutionTime: number;
    errorRate: number;
    lastActivity: Date;
  };
  healthScore: number;
}

export interface ParallelExecutionMetrics {
  totalAgents: number;
  activeAgents: number;
  idleAgents: number;
  overloadedAgents: number;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  queuedTasks: number;
  averageWaitTime: number;
  throughput: number; // tasks per minute
  resourceUtilization: {
    memory: number;
    cpu: number;
    tokens: number;
    apiCalls: number;
  };
  scalingEvents: ScalingEvent[];
}

export interface ScalingEvent {
  timestamp: Date;
  action: 'scale_up' | 'scale_down';
  agentType: string;
  reason: string;
  beforeCount: number;
  afterCount: number;
}

export class ParallelExecutionEngine extends EventEmitter {
  private static instance: ParallelExecutionEngine;
  private config: ParallelExecutionConfig;
  private agentPools: Map<string, AgentPool> = new Map();
  private taskQueue: Map<string, Task[]> = new Map(); // priority queues
  private executionMetrics: ParallelExecutionMetrics;
  private isRunning = false;
  private scalingTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;
  private metricsUpdateTimer?: NodeJS.Timeout;

  public static getInstance(): ParallelExecutionEngine {
    if (!ParallelExecutionEngine.instance) {
      ParallelExecutionEngine.instance = new ParallelExecutionEngine();
    }
    return ParallelExecutionEngine.instance;
  }

  constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.executionMetrics = this.initializeMetrics();
    this.initializeAgentPools();
  }

  private getDefaultConfig(): ParallelExecutionConfig {
    return {
      maxConcurrentAgents: 20,
      maxConcurrentTasksPerAgent: 3,
      resourceLimits: {
        maxMemoryUsage: 2048,
        maxCpuUsage: 80,
        maxTokenUsage: 50000,
        maxApiCalls: 1000
      },
      scalingPolicy: {
        enabled: true,
        minAgents: 2,
        maxAgents: 50,
        scaleUpThreshold: 10,
        scaleDownThreshold: 2,
        cooldownPeriod: 30
      },
      loadBalancing: {
        strategy: 'capability_based',
        enableStickySessions: false,
        healthCheckInterval: 10
      },
      failureHandling: {
        maxRetries: 3,
        retryDelay: 5000,
        circuitBreakerThreshold: 5,
        fallbackStrategy: 'redistribute'
      }
    };
  }

  private initializeMetrics(): ParallelExecutionMetrics {
    return {
      totalAgents: 0,
      activeAgents: 0,
      idleAgents: 0,
      overloadedAgents: 0,
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      queuedTasks: 0,
      averageWaitTime: 0,
      throughput: 0,
      resourceUtilization: {
        memory: 0,
        cpu: 0,
        tokens: 0,
        apiCalls: 0
      },
      scalingEvents: []
    };
  }

  private async initializeAgentPools(): Promise<void> {
    AIAgentLibrary.getInstance();
    const agentTypes = ['architect', 'developer', 'reviewer', 'tester', 'security', 'documentation', 'optimizer'];

    for (const agentType of agentTypes) {
      const pool: AgentPool = {
        id: `pool_${agentType}`,
        agentType,
        instances: [],
        maxInstances: this.config.scalingPolicy.maxAgents,
        currentLoad: 0,
        averageResponseTime: 0,
        successRate: 1.0,
        lastScaleAction: new Date()
      };

      // Initialize with minimum instances
      for (let i = 0; i < this.config.scalingPolicy.minAgents; i++) {
        await this.createAgentInstance(pool);
      }

      this.agentPools.set(agentType, pool);
    }
  }

  private async createAgentInstance(pool: AgentPool): Promise<AgentInstance> {
    const instance: AgentInstance = {
      id: `${pool.agentType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      agentId: pool.agentType,
      status: 'idle',
      currentTasks: [],
      resourceUsage: {
        memory: 0,
        cpu: 0,
        tokens: 0,
        apiCalls: 0
      },
      performance: {
        tasksCompleted: 0,
        averageExecutionTime: 0,
        errorRate: 0,
        lastActivity: new Date()
      },
      healthScore: 1.0
    };

    pool.instances.push(instance);
    return instance;
  }

  public async start(): Promise<void> {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('🚀 Starting Parallel Execution Engine...');

    // Start background processes
    this.startScalingMonitor();
    this.startHealthMonitor();
    this.startMetricsUpdater();
    this.startTaskProcessor();

    console.log('✅ Parallel Execution Engine started successfully');
  }

  public async stop(): Promise<void> {
    this.isRunning = false;
    
    if (this.scalingTimer) clearInterval(this.scalingTimer);
    if (this.healthCheckTimer) clearInterval(this.healthCheckTimer);
    if (this.metricsUpdateTimer) clearInterval(this.metricsUpdateTimer);

    console.log('🛑 Parallel Execution Engine stopped');
  }

  public async submitTasks(tasks: Task[], priority: TaskPriority = 'medium'): Promise<string[]> {
    const taskIds: string[] = [];

    for (const task of tasks) {
      const taskId = await this.submitTask(task, priority);
      taskIds.push(taskId);
    }

    return taskIds;
  }

  public async submitTask(task: Task, priority: TaskPriority = 'medium'): Promise<string> {
    if (!this.taskQueue.has(priority)) {
      this.taskQueue.set(priority, []);
    }

    const queue = this.taskQueue.get(priority)!;
    queue.push(task);

    // Sort by priority within the queue
    queue.sort((a, b) => this.getTaskPriorityScore(b) - this.getTaskPriorityScore(a));

    this.executionMetrics.totalTasks++;
    this.executionMetrics.queuedTasks++;

    // Trigger immediate processing if agents are available
    this.processTaskQueue();

    return task.id;
  }

  private getTaskPriorityScore(task: Task): number {
    const priorityScores: { [key in TaskPriority]: number } = { critical: 4, high: 3, medium: 2, low: 1, urgent: 5 };
    return priorityScores[task.priority] || 2;
  }

  private async processTaskQueue(): Promise<void> {
    const priorities: TaskPriority[] = ['urgent', 'critical', 'high', 'medium', 'low'];

    for (const priority of priorities) {
      const queue = this.taskQueue.get(priority);
      if (!queue || queue.length === 0) continue;

      while (queue.length > 0) {
        const task = queue[0];
        const agent = await this.findBestAgent(task);

        if (!agent) {
          // No available agents, check if we should scale up
          await this.considerScaling(task);
          break;
        }

        // Remove task from queue and assign to agent
        queue.shift();
        this.executionMetrics.queuedTasks--;
        await this.executeTaskOnAgent(task, agent);
      }
    }
  }

  private async findBestAgent(task: Task): Promise<AgentInstance | null> {
    const requiredAgentType = this.getRequiredAgentType(task);
    const pool = this.agentPools.get(requiredAgentType);

    if (!pool) return null;

    const availableAgents = pool.instances.filter(agent => 
      agent.status === 'idle' && 
      agent.currentTasks.length < this.config.maxConcurrentTasksPerAgent &&
      agent.healthScore > 0.5
    );

    if (availableAgents.length === 0) return null;

    // Apply load balancing strategy
    switch (this.config.loadBalancing.strategy) {
      case 'round_robin':
        return availableAgents[0];
      
      case 'least_loaded':
        return availableAgents.reduce((best, current) => 
          current.currentTasks.length < best.currentTasks.length ? current : best
        );
      
      case 'capability_based':
        return availableAgents.reduce((best, current) => 
          current.healthScore > best.healthScore ? current : best
        );
      
      case 'priority_weighted':
        return availableAgents.reduce((best, current) => {
          const currentScore = current.healthScore * (1 / (current.currentTasks.length + 1));
          const bestScore = best.healthScore * (1 / (best.currentTasks.length + 1));
          return currentScore > bestScore ? current : best;
        });
      
      default:
        return availableAgents[0];
    }
  }

  private getRequiredAgentType(task: Task): string {
    const typeMapping: Record<string, string> = {
      'code_review': 'reviewer',
      'code_generation': 'developer',
      'testing': 'tester',
      'security_scan': 'security',
      'documentation': 'documentation',
      'optimization': 'optimizer',
      'refactoring': 'developer',
      'analysis': 'architect'
    };

    return typeMapping[task.type] || 'developer';
  }

  private async executeTaskOnAgent(task: Task, agent: AgentInstance): Promise<void> {
    agent.status = 'busy';
    agent.currentTasks.push(task.id);
    agent.performance.lastActivity = new Date();

    const startTime = Date.now();

    try {
      // Simulate task execution with resource monitoring
      await this.simulateTaskExecution(task, agent);
      
      const executionTime = Date.now() - startTime;
      
      // Update agent performance metrics
      agent.performance.tasksCompleted++;
      agent.performance.averageExecutionTime = 
        (agent.performance.averageExecutionTime + executionTime) / 2;
      
      // Update global metrics
      this.executionMetrics.completedTasks++;
      
      // Update agent health score
      this.updateAgentHealthScore(agent, true);
      
      // Emit task completed event
      this.emit('taskCompleted', {
        taskId: task.id,
        agentId: agent.id,
        executionTime,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error(`Task execution failed on agent ${agent.id}:`, error);
      
      agent.performance.errorRate = 
        (agent.performance.errorRate + 1) / (agent.performance.tasksCompleted + 1);
      
      this.executionMetrics.failedTasks++;
      this.updateAgentHealthScore(agent, false);
      
      // Emit task failed event
      this.emit('taskFailed', {
        taskId: task.id,
        agentId: agent.id,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now()
      });
      
      // Handle task retry or redistribution
      await this.handleTaskFailure(task, agent, error as Error);
    } finally {
      // Clean up
      agent.currentTasks = agent.currentTasks.filter(id => id !== task.id);
      if (agent.currentTasks.length === 0) {
        agent.status = 'idle';
      }
    }
  }

  private async simulateTaskExecution(task: Task, agent: AgentInstance): Promise<void> {
    const executionTime = task.estimated_duration || 5000;
    const steps = 10;
    const stepTime = executionTime / steps;

    for (let i = 0; i < steps; i++) {
      await new Promise(resolve => setTimeout(resolve, stepTime));
      
      // Simulate resource usage
      agent.resourceUsage.memory += Math.random() * 50;
      agent.resourceUsage.cpu += Math.random() * 20;
      agent.resourceUsage.tokens += Math.random() * 100;
      agent.resourceUsage.apiCalls += Math.random() * 5;
      
      // Check resource limits
      if (this.isAgentOverloaded(agent)) {
        agent.status = 'overloaded';
        throw new Error('Agent resource limits exceeded');
      }
    }

    // Reset resource usage after task completion
    agent.resourceUsage = {
      memory: Math.max(0, agent.resourceUsage.memory - 100),
      cpu: Math.max(0, agent.resourceUsage.cpu - 50),
      tokens: agent.resourceUsage.tokens, // Tokens don't reset
      apiCalls: agent.resourceUsage.apiCalls // API calls accumulate
    };
  }

  private isAgentOverloaded(agent: AgentInstance): boolean {
    return (
      agent.resourceUsage.memory > this.config.resourceLimits.maxMemoryUsage ||
      agent.resourceUsage.cpu > this.config.resourceLimits.maxCpuUsage ||
      agent.resourceUsage.tokens > this.config.resourceLimits.maxTokenUsage ||
      agent.resourceUsage.apiCalls > this.config.resourceLimits.maxApiCalls
    );
  }

  private updateAgentHealthScore(agent: AgentInstance, success: boolean): void {
    const factor = success ? 0.1 : -0.2;
    agent.healthScore = Math.max(0, Math.min(1, agent.healthScore + factor));
  }

  private async handleTaskFailure(task: Task, agent: AgentInstance, error: Error): Promise<void> {
    switch (this.config.failureHandling.fallbackStrategy) {
      case 'redistribute':
        // Try to find another agent
        const newAgent = await this.findBestAgent(task);
        if (newAgent && newAgent.id !== agent.id) {
          setTimeout(() => this.executeTaskOnAgent(task, newAgent), this.config.failureHandling.retryDelay);
        } else {
          // No other agent available, requeue the task
          await this.submitTask(task, task.priority);
        }
        break;
      
      case 'queue':
        // Requeue the task for later retry
        setTimeout(() => this.submitTask(task, task.priority), this.config.failureHandling.retryDelay);
        break;
      
      case 'fail_fast':
        // Mark task as permanently failed
        console.error(`Task ${task.id} failed permanently:`, error);
        break;
    }
  }

  private async considerScaling(task: Task): Promise<void> {
    if (!this.config.scalingPolicy.enabled) return;

    const requiredAgentType = this.getRequiredAgentType(task);
    const pool = this.agentPools.get(requiredAgentType);
    
    if (!pool) return;

    const queueLength = this.getQueueLength();
    const timeSinceLastScale = Date.now() - pool.lastScaleAction.getTime();
    
    if (queueLength > this.config.scalingPolicy.scaleUpThreshold &&
        pool.instances.length < pool.maxInstances &&
        timeSinceLastScale > this.config.scalingPolicy.cooldownPeriod * 1000) {
      
      await this.scaleUp(pool);
    }
  }

  private async scaleUp(pool: AgentPool): Promise<void> {
    await this.createAgentInstance(pool);
    pool.lastScaleAction = new Date();
    
    const scalingEvent: ScalingEvent = {
      timestamp: new Date(),
      action: 'scale_up',
      agentType: pool.agentType,
      reason: 'High queue length',
      beforeCount: pool.instances.length - 1,
      afterCount: pool.instances.length
    };
    
    this.executionMetrics.scalingEvents.push(scalingEvent);
    console.log(`🔼 Scaled up ${pool.agentType} pool to ${pool.instances.length} instances`);
    
    // Emit agent scaled event
    this.emit('agentScaled', scalingEvent);
  }

  private async scaleDown(pool: AgentPool): Promise<void> {
    const idleAgents = pool.instances.filter(agent => agent.status === 'idle');
    
    if (idleAgents.length > this.config.scalingPolicy.minAgents) {
      const agentToRemove = idleAgents[0];
      pool.instances = pool.instances.filter(agent => agent.id !== agentToRemove.id);
      pool.lastScaleAction = new Date();
      
      const scalingEvent: ScalingEvent = {
        timestamp: new Date(),
        action: 'scale_down',
        agentType: pool.agentType,
        reason: 'Low queue length',
        beforeCount: pool.instances.length + 1,
        afterCount: pool.instances.length
      };
      
      this.executionMetrics.scalingEvents.push(scalingEvent);
      console.log(`🔽 Scaled down ${pool.agentType} pool to ${pool.instances.length} instances`);
      
      // Emit agent scaled event
      this.emit('agentScaled', scalingEvent);
    }
  }

  private getQueueLength(): number {
    return Array.from(this.taskQueue.values()).reduce((total, queue) => total + queue.length, 0);
  }

  private startScalingMonitor(): void {
    this.scalingTimer = setInterval(async () => {
      if (!this.config.scalingPolicy.enabled) return;

      const queueLength = this.getQueueLength();
      
      for (const pool of this.agentPools.values()) {
        const timeSinceLastScale = Date.now() - pool.lastScaleAction.getTime();
        
        if (timeSinceLastScale < this.config.scalingPolicy.cooldownPeriod * 1000) {
          continue;
        }

        if (queueLength > this.config.scalingPolicy.scaleUpThreshold &&
            pool.instances.length < pool.maxInstances) {
          await this.scaleUp(pool);
        } else if (queueLength < this.config.scalingPolicy.scaleDownThreshold &&
                   pool.instances.length > this.config.scalingPolicy.minAgents) {
          await this.scaleDown(pool);
        }
      }
    }, 5000); // Check every 5 seconds
  }

  private startHealthMonitor(): void {
    this.healthCheckTimer = setInterval(() => {
      for (const pool of this.agentPools.values()) {
        for (const agent of pool.instances) {
          // Check if agent is stuck
          const timeSinceActivity = Date.now() - agent.performance.lastActivity.getTime();
          if (timeSinceActivity > 60000 && agent.status === 'busy') { // 1 minute
            console.warn(`Agent ${agent.id} appears to be stuck, resetting...`);
            agent.status = 'idle';
            agent.currentTasks = [];
            agent.healthScore = Math.max(0.1, agent.healthScore - 0.3);
          }
          
          // Reset overloaded agents if resources have recovered
          if (agent.status === 'overloaded' && !this.isAgentOverloaded(agent)) {
            agent.status = 'idle';
            agent.healthScore = Math.min(1.0, agent.healthScore + 0.1);
          }
        }
      }
    }, this.config.loadBalancing.healthCheckInterval * 1000);
  }

  private startMetricsUpdater(): void {
    this.metricsUpdateTimer = setInterval(() => {
      this.updateMetrics();
    }, 1000); // Update every second
  }

  private updateMetrics(): void {
    let totalAgents = 0;
    let activeAgents = 0;
    let idleAgents = 0;
    let overloadedAgents = 0;

    for (const pool of this.agentPools.values()) {
      totalAgents += pool.instances.length;
      
      for (const agent of pool.instances) {
        switch (agent.status) {
          case 'busy':
            activeAgents++;
            break;
          case 'idle':
            idleAgents++;
            break;
          case 'overloaded':
            overloadedAgents++;
            break;
        }
      }
    }

    this.executionMetrics.totalAgents = totalAgents;
    this.executionMetrics.activeAgents = activeAgents;
    this.executionMetrics.idleAgents = idleAgents;
    this.executionMetrics.overloadedAgents = overloadedAgents;
    this.executionMetrics.queuedTasks = this.getQueueLength();
  }

  private startTaskProcessor(): void {
    const processLoop = async () => {
      if (this.isRunning) {
        await this.processTaskQueue();
        setTimeout(processLoop, 1000); // Process every second
      }
    };
    processLoop();
  }

  // Public API methods
  public getMetrics(): ParallelExecutionMetrics {
    return { ...this.executionMetrics };
  }

  public getAgentPools(): Map<string, AgentPool> {
    return new Map(this.agentPools);
  }

  public updateConfig(newConfig: Partial<ParallelExecutionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): ParallelExecutionConfig {
    return { ...this.config };
  }

  public async pauseExecution(): Promise<void> {
    this.isRunning = false;
    console.log('⏸️ Parallel execution paused');
  }

  public async resumeExecution(): Promise<void> {
    if (!this.isRunning) {
      this.isRunning = true;
      this.startTaskProcessor();
      console.log('▶️ Parallel execution resumed');
    }
  }

  public getQueueStatus(): Record<string, number> {
    const status: Record<string, number> = {};
    for (const [priority, queue] of this.taskQueue.entries()) {
      status[priority] = queue.length;
    }
    return status;
  }

  public async clearQueue(priority?: string): Promise<void> {
    if (priority) {
      this.taskQueue.set(priority, []);
    } else {
      this.taskQueue.clear();
    }
    this.executionMetrics.queuedTasks = this.getQueueLength();
  }
}
