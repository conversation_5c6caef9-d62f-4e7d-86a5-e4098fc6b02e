import { BaseAgent, AgentType, AgentCategory, PerformanceMetrics } from './types';

export class AIAgentLibrary {
  private static instance: AIAgentLibrary;
  private agents: Map<string, BaseAgent> = new Map();
  private categories: Map<string, AgentCategory> = new Map();

  public static getInstance(): AIAgentLibrary {
    if (!AIAgentLibrary.instance) {
      AIAgentLibrary.instance = new AIAgentLibrary();
      AIAgentLibrary.instance.initializeDefaultAgents();
    }
    return AIAgentLibrary.instance;
  }

  private initializeDefaultAgents(): void {
    // Code Quality Agents
    this.registerAgent(this.createCodeReviewerAgent());
    this.registerAgent(this.createRefactorerAgent());
    this.registerAgent(this.createOptimizerAgent());
    this.registerAgent(this.createSecurityScannerAgent());
    
    // Architecture Agents
    this.registerAgent(this.createArchitectAgent());
    this.registerAgent(this.createAPIDesignerAgent());
    this.registerAgent(this.createDatabaseDesignerAgent());
    
    // Testing Agents
    this.registerAgent(this.createUnitTesterAgent());
    this.registerAgent(this.createIntegrationTesterAgent());
    this.registerAgent(this.createE2ETesterAgent());
    this.registerAgent(this.createPerformanceTesterAgent());
    
    // Documentation Agents
    this.registerAgent(this.createDocumenterAgent());
    this.registerAgent(this.createAPIDocAgent());
    this.registerAgent(this.createReadmeGeneratorAgent());
    
    // DevOps Agents
    this.registerAgent(this.createDeploymentAgent());
    this.registerAgent(this.createMonitoringAgent());
    this.registerAgent(this.createCIAgent());
    
    // Data Agents
    this.registerAgent(this.createDataAnalystAgent());
    this.registerAgent(this.createDataValidatorAgent());
    this.registerAgent(this.createDataTransformerAgent());
    
    // UI/UX Agents
    this.registerAgent(this.createUIDesignerAgent());
    this.registerAgent(this.createAccessibilityAgent());
    this.registerAgent(this.createResponsiveDesignAgent());
    
    // Task Management Agents
    this.registerAgent(this.createTaskMasterAgent());
    this.registerAgent(this.createOrchestratorAgent());
    this.registerAgent(this.createWorkflowManagerAgent());
    
    // Specialized Agents
    this.registerAgent(this.createTranslatorAgent());
    this.registerAgent(this.createResearcherAgent());
    this.registerAgent(this.createDebuggerAgent());

    this.initializeCategories();
  }

  private createCodeReviewerAgent(): BaseAgent {
    return {
      id: 'code-reviewer-v1',
      name: 'Code Reviewer',
      type: 'code-reviewer',
      description: 'Comprehensive code review with best practices, security, and performance analysis',
      capabilities: [
        'Static code analysis',
        'Security vulnerability detection',
        'Performance optimization suggestions',
        'Code style and formatting',
        'Best practices enforcement',
        'Documentation quality check'
      ],
      status: 'idle',
      config: {
        model: 'sonnet',
        temperature: 0.1,
        max_tokens: 4000,
        timeout: 300,
        retry_count: 3,
        parallel_execution: true,
        dependencies: [],
        triggers: [
          {
            type: 'file_change',
            condition: '*.{js,ts,py,java,cpp,c,go,rs}',
            parameters: { auto_review: true }
          }
        ],
        outputs: [
          {
            type: 'report',
            format: 'markdown',
            destination: 'reviews/',
            template: 'code_review_template'
          }
        ],
        custom_prompts: {
          system: 'You are an expert code reviewer. Analyze code for security, performance, maintainability, and best practices.',
          review: 'Review this code and provide detailed feedback with specific suggestions for improvement.'
        },
        tools: ['static_analysis', 'security_scanner', 'performance_profiler'],
        mcp_servers: ['filesystem', 'git', 'github']
      },
      metadata: {
        author: 'Claude Code Team',
        version: '1.0.0',
        tags: ['code-quality', 'security', 'performance'],
        category: 'Code Quality',
        difficulty: 'intermediate',
        estimated_duration: 300,
        success_rate: 0.95,
        usage_count: 0,
        last_used: '',
        performance_metrics: {
          average_execution_time: 180,
          success_rate: 0.95,
          error_rate: 0.05,
          resource_usage: {
            cpu_usage: 0.3,
            memory_usage: 0.2,
            token_usage: 2000,
            api_calls: 5
          },
          quality_score: 0.92
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  private createArchitectAgent(): BaseAgent {
    return {
      id: 'architect-v1',
      name: 'Software Architect',
      type: 'architect',
      description: 'System architecture design and analysis with scalability and maintainability focus',
      capabilities: [
        'System architecture design',
        'Scalability analysis',
        'Technology stack recommendations',
        'Design pattern suggestions',
        'Microservices architecture',
        'Database design',
        'API architecture'
      ],
      status: 'idle',
      config: {
        model: 'opus',
        temperature: 0.3,
        max_tokens: 6000,
        timeout: 600,
        retry_count: 2,
        parallel_execution: false,
        dependencies: [],
        triggers: [
          {
            type: 'manual',
            condition: 'architecture_review_requested',
            parameters: {}
          }
        ],
        outputs: [
          {
            type: 'report',
            format: 'markdown',
            destination: 'architecture/',
            template: 'architecture_analysis_template'
          },
          {
            type: 'file',
            format: 'mermaid',
            destination: 'diagrams/',
            template: 'architecture_diagram_template'
          }
        ],
        custom_prompts: {
          system: 'You are a senior software architect with expertise in scalable system design.',
          analyze: 'Analyze the system architecture and provide recommendations for improvement.'
        },
        tools: ['diagram_generator', 'dependency_analyzer', 'performance_estimator'],
        mcp_servers: ['filesystem', 'github', 'database']
      },
      metadata: {
        author: 'Claude Code Team',
        version: '1.0.0',
        tags: ['architecture', 'design', 'scalability'],
        category: 'Architecture',
        difficulty: 'expert',
        estimated_duration: 900,
        success_rate: 0.88,
        usage_count: 0,
        last_used: '',
        performance_metrics: {
          average_execution_time: 720,
          success_rate: 0.88,
          error_rate: 0.12,
          resource_usage: {
            cpu_usage: 0.4,
            memory_usage: 0.3,
            token_usage: 4500,
            api_calls: 8
          },
          quality_score: 0.91
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  private createTaskMasterAgent(): BaseAgent {
    return {
      id: 'task-master-v1',
      name: 'Task Master',
      type: 'task-master',
      description: 'Intelligent task orchestration and workflow management with dependency resolution',
      capabilities: [
        'Task dependency resolution',
        'Workflow orchestration',
        'Resource allocation',
        'Progress tracking',
        'Error handling and recovery',
        'Performance optimization',
        'Parallel execution management'
      ],
      status: 'idle',
      config: {
        model: 'sonnet',
        temperature: 0.2,
        max_tokens: 3000,
        timeout: 120,
        retry_count: 5,
        parallel_execution: true,
        dependencies: [],
        triggers: [
          {
            type: 'event',
            condition: 'workflow_started',
            parameters: { auto_orchestrate: true }
          }
        ],
        outputs: [
          {
            type: 'data',
            format: 'json',
            destination: 'workflows/',
            template: 'execution_report_template'
          }
        ],
        custom_prompts: {
          system: 'You are a task orchestration expert responsible for managing complex workflows.',
          orchestrate: 'Analyze the workflow and optimize task execution order and resource allocation.'
        },
        tools: ['dependency_resolver', 'resource_monitor', 'execution_planner'],
        mcp_servers: ['filesystem', 'memory', 'performance']
      },
      metadata: {
        author: 'Claude Code Team',
        version: '1.0.0',
        tags: ['orchestration', 'workflow', 'automation'],
        category: 'Task Management',
        difficulty: 'advanced',
        estimated_duration: 60,
        success_rate: 0.97,
        usage_count: 0,
        last_used: '',
        performance_metrics: {
          average_execution_time: 45,
          success_rate: 0.97,
          error_rate: 0.03,
          resource_usage: {
            cpu_usage: 0.2,
            memory_usage: 0.15,
            token_usage: 1500,
            api_calls: 3
          },
          quality_score: 0.94
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Additional agent creation methods...
  private createRefactorerAgent(): BaseAgent {
    return {
      id: 'refactorer-v1',
      name: 'Code Refactorer',
      type: 'refactorer',
      description: 'Intelligent code refactoring with pattern recognition and optimization',
      capabilities: [
        'Code smell detection',
        'Design pattern application',
        'Performance optimization',
        'Code duplication removal',
        'Method extraction',
        'Class restructuring'
      ],
      status: 'idle',
      config: {
        model: 'sonnet',
        temperature: 0.1,
        max_tokens: 4000,
        timeout: 400,
        retry_count: 3,
        parallel_execution: true,
        dependencies: ['code-reviewer-v1'],
        triggers: [
          {
            type: 'manual',
            condition: 'refactoring_requested',
            parameters: { preserve_functionality: true }
          }
        ],
        outputs: [
          {
            type: 'file',
            format: 'source_code',
            destination: 'refactored/',
            template: 'refactored_code_template'
          }
        ],
        custom_prompts: {
          system: 'You are an expert code refactoring specialist focused on improving code quality.',
          refactor: 'Refactor this code to improve readability, maintainability, and performance.'
        },
        tools: ['ast_parser', 'pattern_detector', 'code_formatter'],
        mcp_servers: ['filesystem', 'git']
      },
      metadata: {
        author: 'Claude Code Team',
        version: '1.0.0',
        tags: ['refactoring', 'optimization', 'clean-code'],
        category: 'Code Quality',
        difficulty: 'advanced',
        estimated_duration: 240,
        success_rate: 0.91,
        usage_count: 0,
        last_used: '',
        performance_metrics: {
          average_execution_time: 200,
          success_rate: 0.91,
          error_rate: 0.09,
          resource_usage: {
            cpu_usage: 0.35,
            memory_usage: 0.25,
            token_usage: 3000,
            api_calls: 6
          },
          quality_score: 0.89
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Placeholder methods for other agents (implement as needed)
  private createOptimizerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createSecurityScannerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createAPIDesignerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDatabaseDesignerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createUnitTesterAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createIntegrationTesterAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createE2ETesterAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createPerformanceTesterAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDocumenterAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createAPIDocAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createReadmeGeneratorAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDeploymentAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createMonitoringAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createCIAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDataAnalystAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDataValidatorAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDataTransformerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createUIDesignerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createAccessibilityAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createResponsiveDesignAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createOrchestratorAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createWorkflowManagerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createTranslatorAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createResearcherAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }
  private createDebuggerAgent(): BaseAgent { /* Implementation */ return {} as BaseAgent; }

  private initializeCategories(): void {
    this.categories.set('code-quality', {
      id: 'code-quality',
      name: 'Code Quality',
      description: 'Agents focused on improving code quality, security, and performance',
      icon: 'code',
      agents: ['code-reviewer-v1', 'refactorer-v1', 'optimizer-v1', 'security-scanner-v1']
    });

    this.categories.set('architecture', {
      id: 'architecture',
      name: 'Architecture & Design',
      description: 'System architecture and design pattern specialists',
      icon: 'blueprint',
      agents: ['architect-v1', 'api-designer-v1', 'database-designer-v1']
    });

    this.categories.set('testing', {
      id: 'testing',
      name: 'Testing & QA',
      description: 'Comprehensive testing and quality assurance agents',
      icon: 'test-tube',
      agents: ['unit-tester-v1', 'integration-tester-v1', 'e2e-tester-v1', 'performance-tester-v1']
    });

    this.categories.set('documentation', {
      id: 'documentation',
      name: 'Documentation',
      description: 'Documentation generation and maintenance specialists',
      icon: 'book',
      agents: ['documenter-v1', 'api-doc-v1', 'readme-generator-v1']
    });

    this.categories.set('devops', {
      id: 'devops',
      name: 'DevOps & Deployment',
      description: 'Deployment, monitoring, and CI/CD automation agents',
      icon: 'server',
      agents: ['deployment-v1', 'monitoring-v1', 'ci-v1']
    });

    this.categories.set('task-management', {
      id: 'task-management',
      name: 'Task Management',
      description: 'Workflow orchestration and task automation specialists',
      icon: 'workflow',
      agents: ['task-master-v1', 'orchestrator-v1', 'workflow-manager-v1']
    });
  }

  // Public API methods
  public registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.id, agent);
  }

  public getAgent(id: string): BaseAgent | undefined {
    return this.agents.get(id);
  }

  public getAllAgents(): BaseAgent[] {
    return Array.from(this.agents.values());
  }

  public getAgentsByType(type: AgentType): BaseAgent[] {
    return this.getAllAgents().filter(agent => agent.type === type);
  }

  public getAgentsByCategory(categoryId: string): BaseAgent[] {
    const category = this.categories.get(categoryId);
    if (!category) return [];
    
    return category.agents
      .map(id => this.getAgent(id))
      .filter((agent): agent is BaseAgent => agent !== undefined);
  }

  public searchAgents(query: string): BaseAgent[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllAgents().filter(agent => 
      agent.name.toLowerCase().includes(lowerQuery) ||
      agent.description.toLowerCase().includes(lowerQuery) ||
      agent.capabilities.some(cap => cap.toLowerCase().includes(lowerQuery)) ||
      agent.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  public getCategories(): AgentCategory[] {
    return Array.from(this.categories.values());
  }

  public getFeaturedAgents(): BaseAgent[] {
    // Return top-rated agents
    return this.getAllAgents()
      .sort((a, b) => b.metadata.performance_metrics.quality_score - a.metadata.performance_metrics.quality_score)
      .slice(0, 6);
  }

  public getPopularAgents(): BaseAgent[] {
    // Return most used agents
    return this.getAllAgents()
      .sort((a, b) => b.metadata.usage_count - a.metadata.usage_count)
      .slice(0, 10);
  }

  public getRecentAgents(): BaseAgent[] {
    // Return recently used agents
    return this.getAllAgents()
      .filter(agent => agent.metadata.last_used)
      .sort((a, b) => new Date(b.metadata.last_used).getTime() - new Date(a.metadata.last_used).getTime())
      .slice(0, 5);
  }

  public updateAgentMetrics(agentId: string, metrics: Partial<PerformanceMetrics>): void {
    const agent = this.getAgent(agentId);
    if (agent) {
      agent.metadata.performance_metrics = {
        ...agent.metadata.performance_metrics,
        ...metrics
      };
      agent.metadata.usage_count += 1;
      agent.metadata.last_used = new Date().toISOString();
      agent.updated_at = new Date().toISOString();
    }
  }
}