import { dataEventBus, DataEventType } from '../EventBus';

export type HookPhase = 'pre' | 'during' | 'post' | 'error' | 'finally';
export type HookOperation = 
  | 'connect' | 'disconnect' | 'query' | 'insert' | 'update' | 'delete' 
  | 'transform' | 'validate' | 'stream' | 'batch' | 'transaction';

export interface HookContext {
  operation: HookOperation;
  phase: HookPhase;
  connector?: string;
  data?: any;
  metadata?: Record<string, any>;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  error?: Error;
  result?: any;
}

export interface HookHandler {
  name: string;
  priority: number;
  enabled: boolean;
  operations?: HookOperation[];
  phases?: HookPhase[];
  handler: (context: HookContext) => void | Promise<void>;
}

export interface HookRegistration {
  id: string;
  unregister: () => void;
}

export class DataOperationHooks {
  private static instance: DataOperationHooks;
  private hooks: Map<string, HookHandler> = new Map();
  private executionOrder: string[] = [];
  
  private constructor() {
    this.setupDefaultHooks();
  }
  
  static getInstance(): DataOperationHooks {
    if (!DataOperationHooks.instance) {
      DataOperationHooks.instance = new DataOperationHooks();
    }
    return DataOperationHooks.instance;
  }
  
  register(handler: Omit<HookHandler, 'enabled'> & { enabled?: boolean }): HookRegistration {
    const id = `hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullHandler: HookHandler = {
      ...handler,
      enabled: handler.enabled ?? true
    };
    
    this.hooks.set(id, fullHandler);
    this.updateExecutionOrder();
    
    return {
      id,
      unregister: () => this.unregister(id)
    };
  }
  
  unregister(id: string): void {
    this.hooks.delete(id);
    this.updateExecutionOrder();
  }
  
  async execute(context: HookContext): Promise<void> {
    const applicableHooks = this.getApplicableHooks(context);
    
    for (const hookId of applicableHooks) {
      const hook = this.hooks.get(hookId);
      if (!hook || !hook.enabled) continue;
      
      try {
        await hook.handler(context);
      } catch (error) {
        console.error(`Hook ${hook.name} failed:`, error);
        // Continue with other hooks even if one fails
      }
    }
  }
  
  private getApplicableHooks(context: HookContext): string[] {
    return this.executionOrder.filter(id => {
      const hook = this.hooks.get(id);
      if (!hook || !hook.enabled) return false;
      
      // Check if hook applies to this operation
      if (hook.operations && !hook.operations.includes(context.operation)) {
        return false;
      }
      
      // Check if hook applies to this phase
      if (hook.phases && !hook.phases.includes(context.phase)) {
        return false;
      }
      
      return true;
    });
  }
  
  private updateExecutionOrder(): void {
    this.executionOrder = Array.from(this.hooks.entries())
      .sort(([, a], [, b]) => b.priority - a.priority)
      .map(([id]) => id);
  }
  
  private setupDefaultHooks(): void {
    // Audit logging hook
    this.register({
      name: 'audit-logger',
      priority: 100,
      phases: ['post', 'error'],
      handler: async (context) => {
        dataEventBus.emit(DataEventType.AUDIT_LOG_CREATED, {
          operation: context.operation,
          phase: context.phase,
          connector: context.connector,
          duration: context.duration,
          success: context.phase === 'post',
          error: context.error?.message
        });
      }
    });
    
    // Performance monitoring hook
    this.register({
      name: 'performance-monitor',
      priority: 90,
      phases: ['post'],
      handler: async (context) => {
        if (context.duration && context.duration > 5000) {
          dataEventBus.emit(DataEventType.PERFORMANCE_THRESHOLD_EXCEEDED, {
            operation: context.operation,
            connector: context.connector,
            duration: context.duration,
            threshold: 5000
          });
        }
      }
    });
    
    // Data access logging hook
    this.register({
      name: 'data-access-logger',
      priority: 80,
      operations: ['query', 'insert', 'update', 'delete'],
      phases: ['pre'],
      handler: async (context) => {
        dataEventBus.emit(DataEventType.DATA_ACCESS_LOGGED, {
          operation: context.operation,
          connector: context.connector,
          timestamp: new Date()
        });
      }
    });
  }
  
  // Convenience methods for common hook patterns
  addValidationHook(
    validator: (data: any) => Promise<{ valid: boolean; errors?: any[] }>,
    operations: HookOperation[] = ['insert', 'update']
  ): HookRegistration {
    return this.register({
      name: 'custom-validation',
      priority: 50,
      operations,
      phases: ['pre'],
      handler: async (context) => {
        if (context.data) {
          const result = await validator(context.data);
          if (!result.valid) {
            throw new Error(`Validation failed: ${JSON.stringify(result.errors)}`);
          }
        }
      }
    });
  }
  
  addTransformHook(
    transformer: (data: any) => any,
    operations: HookOperation[] = ['insert', 'update']
  ): HookRegistration {
    return this.register({
      name: 'custom-transform',
      priority: 40,
      operations,
      phases: ['pre'],
      handler: async (context) => {
        if (context.data) {
          context.data = transformer(context.data);
        }
      }
    });
  }
  
  addMetricsHook(
    metricsCollector: (context: HookContext) => void
  ): HookRegistration {
    return this.register({
      name: 'custom-metrics',
      priority: 30,
      phases: ['post', 'error'],
      handler: async (context) => {
        metricsCollector(context);
      }
    });
  }
  
  addRetryHook(
    shouldRetry: (error: Error) => boolean,
    maxRetries: number = 3
  ): HookRegistration {
    const retryCount = new Map<string, number>();
    
    return this.register({
      name: 'custom-retry',
      priority: 20,
      phases: ['error'],
      handler: async (context) => {
        if (context.error && shouldRetry(context.error)) {
          const key = `${context.operation}_${context.connector}_${Date.now()}`;
          const attempts = retryCount.get(key) || 0;
          
          if (attempts < maxRetries) {
            retryCount.set(key, attempts + 1);
            throw new Error(`Retry attempt ${attempts + 1} of ${maxRetries}`);
          }
        }
      }
    });
  }
}

// Export singleton instance
export const dataOperationHooks = DataOperationHooks.getInstance();

// Helper class for executing operations with hooks
export class HookedOperation<T> {
  constructor(
    private operation: HookOperation,
    private connector?: string
  ) {}
  
  async execute(
    fn: () => Promise<T>,
    data?: any,
    metadata?: Record<string, any>
  ): Promise<T> {
    const context: HookContext = {
      operation: this.operation,
      phase: 'pre',
      connector: this.connector,
      data,
      metadata,
      startTime: new Date()
    };
    
    try {
      // Pre-operation hooks
      await dataOperationHooks.execute(context);
      
      // During-operation hooks
      context.phase = 'during';
      await dataOperationHooks.execute(context);
      
      // Execute the actual operation
      const result = await fn();
      
      // Post-operation hooks
      context.phase = 'post';
      context.result = result;
      context.endTime = new Date();
      context.duration = context.endTime.getTime() - (context.startTime?.getTime() || 0);
      await dataOperationHooks.execute(context);
      
      return result;
    } catch (error) {
      // Error hooks
      context.phase = 'error';
      context.error = error instanceof Error ? error : new Error(String(error));
      context.endTime = new Date();
      context.duration = context.endTime.getTime() - (context.startTime?.getTime() || 0);
      await dataOperationHooks.execute(context);
      
      throw error;
    } finally {
      // Finally hooks
      context.phase = 'finally';
      await dataOperationHooks.execute(context);
    }
  }
}