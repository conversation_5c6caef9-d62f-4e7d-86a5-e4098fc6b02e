import { EventEmitter } from '../utils/EventEmitter';

export interface DataEvent {
  id: string;
  timestamp: Date;
  type: DataEventType;
  source: string;
  data: any;
  metadata?: Record<string, any>;
}

export enum DataEventType {
  // Data operation events
  DATA_OPERATION_STARTED = 'data.operation.started',
  DATA_OPERATION_COMPLETED = 'data.operation.completed',
  DATA_OPERATION_FAILED = 'data.operation.failed',
  DATA_OPERATION_RETRY = 'data.operation.retry',
  
  // Data validation events
  DATA_VALIDATION_STARTED = 'data.validation.started',
  DATA_VALIDATION_PASSED = 'data.validation.passed',
  DATA_VALIDATION_FAILED = 'data.validation.failed',
  
  // Data transformation events
  DATA_TRANSFORM_STARTED = 'data.transform.started',
  DATA_TRANSFORM_COMPLETED = 'data.transform.completed',
  DATA_TRANSFORM_FAILED = 'data.transform.failed',
  
  // Connection events
  CONNECTION_ESTABLISHED = 'connection.established',
  CONNECTION_LOST = 'connection.lost',
  CONNECTION_RESTORED = 'connection.restored',
  
  // Schema events
  SCHEMA_CHANGED = 'schema.changed',
  SCHEMA_VALIDATION_ERROR = 'schema.validation.error',
  
  // Performance events
  PERFORMANCE_THRESHOLD_EXCEEDED = 'performance.threshold.exceeded',
  MEMORY_LIMIT_WARNING = 'memory.limit.warning',
  
  // Audit events
  AUDIT_LOG_CREATED = 'audit.log.created',
  DATA_ACCESS_LOGGED = 'data.access.logged',
}

export interface EventHandler<T = any> {
  (event: DataEvent & { data: T }): void | Promise<void>;
}

export interface EventSubscription {
  unsubscribe(): void;
}

export class DataEventBus extends EventEmitter {
  private static instance: DataEventBus;
  private eventHistory: DataEvent[] = [];
  private maxHistorySize = 1000;
  
  private constructor() {
    super();
    this.setMaxListeners(100); // Increase default limit
  }
  
  static getInstance(): DataEventBus {
    if (!DataEventBus.instance) {
      DataEventBus.instance = new DataEventBus();
    }
    return DataEventBus.instance;
  }
  
  emit(eventType: DataEventType, data: any, metadata?: Record<string, any>): boolean {
    const event: DataEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type: eventType,
      source: this.getEventSource(),
      data,
      metadata
    };
    
    // Store in history
    this.addToHistory(event);
    
    // Emit to listeners
    return super.emit(eventType, event);
  }
  
  // Override with compatible return types
  on(eventType: DataEventType, handler: EventHandler): this {
    super.on(eventType, handler);
    return this;
  }
  
  once(eventType: DataEventType, handler: EventHandler): this {
    super.once(eventType, handler);
    return this;
  }
  
  // Alternative subscription method with EventSubscription return type
  subscribe(eventType: DataEventType, handler: EventHandler): EventSubscription {
    this.on(eventType, handler);
    return {
      unsubscribe: () => this.off(eventType, handler)
    };
  }
  
  subscribeOnce(eventType: DataEventType, handler: EventHandler): EventSubscription {
    this.once(eventType, handler);
    return {
      unsubscribe: () => this.off(eventType, handler)
    };
  }
  
  off(eventType: DataEventType, handler: EventHandler): this {
    return super.off(eventType, handler);
  }
  
  // Middleware support for event processing
  use(middleware: (event: DataEvent, next: () => void) => void): void {
    const originalEmit = this.emit.bind(this);
    this.emit = (eventType: DataEventType, data: any, metadata?: Record<string, any>) => {
      let called = false;
      const next = () => {
        called = true;
        return originalEmit(eventType, data, metadata);
      };
      
      middleware({ 
        id: this.generateEventId(),
        timestamp: new Date(),
        type: eventType,
        source: this.getEventSource(),
        data,
        metadata
      }, next);
      
      if (!called) {
        return false;
      }
      return true;
    };
  }
  
  // Get event history
  getHistory(filter?: { 
    type?: DataEventType; 
    since?: Date; 
    limit?: number 
  }): DataEvent[] {
    let history = [...this.eventHistory];
    
    if (filter?.type) {
      history = history.filter(e => e.type === filter.type);
    }
    
    if (filter?.since) {
      history = history.filter(e => e.timestamp >= filter.since!);
    }
    
    if (filter?.limit) {
      history = history.slice(-filter.limit);
    }
    
    return history;
  }
  
  // Clear event history
  clearHistory(): void {
    this.eventHistory = [];
  }
  
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
  
  private getEventSource(): string {
    // In a real implementation, this would identify the actual source
    return 'data-integration-system';
  }
  
  private addToHistory(event: DataEvent): void {
    this.eventHistory.push(event);
    
    // Maintain max history size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }
}

// Export singleton instance
export const dataEventBus = DataEventBus.getInstance();