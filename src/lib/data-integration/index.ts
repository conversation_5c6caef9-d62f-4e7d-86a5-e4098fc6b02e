// Event System
export { 
  DataEventBus, 
  dataEventBus,
  DataEventType
} from './EventBus';
export type { 
  <PERSON>E<PERSON>, 
  EventHandler, 
  EventSubscription
} from './EventBus';

// Data Connectors
export { 
  DataConnector
} from './connectors/DataConnector';
export type {
  ConnectionConfig,
  QueryResult,
  DataRow,
  HealthStatus,
  DataConnectorOptions
} from './connectors/DataConnector';

export { 
  PostgreSQLConnector,
  createPostgreSQLConnector
} from './connectors/PostgreSQLConnector';

// Data Validation
export {
  DataValidator
} from './validation/DataValidator';
export type {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  BusinessRule,
  QualityMetric,
  QualityReport
} from './validation/DataValidator';

// Create instances to avoid undefined errors
import { DataValidator, commonSchemas as validatorSchemas } from './validation/DataValidator';
import { dataEventBus, DataEventType } from './EventBus';
import { dataOperationHooks } from './hooks/DataOperationHooks';

export const dataValidator = new DataValidator();
export const commonSchemas = validatorSchemas;

// Resilience Patterns
export {
  CircuitBreaker,
  CircuitBreakerFactory
} from './resilience/CircuitBreaker';
export type {
  CircuitBreakerOptions,
  CircuitBreakerMetrics,
  CircuitBreakerError,
  CircuitState
} from './resilience/CircuitBreaker';

// Hooks System
export {
  DataOperationHooks,
  dataOperationHooks
} from './hooks/DataOperationHooks';
export type {
  HookPhase,
  HookOperation,
  HookContext,
  HookHandler,
  HookRegistration,
  HookedOperation
} from './hooks/DataOperationHooks';

// Re-export types for convenience
export type { ZodSchema } from 'zod';

// Utility functions
export { z } from 'zod';

// Example usage and setup
export const setupDataIntegration = () => {
  // Register default validation schemas
  try {
    dataValidator.registerSchema('user', commonSchemas.dataSource);
  } catch (e) {
    console.warn('Failed to register schema:', e);
  }
  
  // Register default business rules
  try {
    dataValidator.registerBusinessRules('user', [
      {
        id: 'active_check',
        name: 'Active Source Check',
        condition: (data) => data.active === true,
        errorMessage: 'Data source must be active',
        severity: 'warning'
      }
    ]);
  } catch (e) {
    console.warn('Failed to register business rules:', e);
  }
  
  // Register default quality metrics
  try {
    dataValidator.registerQualityMetrics('user', [
    {
      id: 'completeness',
      name: 'Data Completeness',
      calculate: (data) => {
        const requiredFields = ['id', 'name', 'type'];
        const presentFields = requiredFields.filter(field => data[field] != null);
        return presentFields.length / requiredFields.length;
      },
      threshold: 1.0,
      operator: 'gte'
    }
    ]);
  } catch (e) {
    console.warn('Failed to register quality metrics:', e);
  }
  
  // Set up event listeners
  try {
    dataEventBus.on(DataEventType.DATA_OPERATION_FAILED, (event: any) => {
    console.error('Data operation failed:', event.data);
  });
  
    dataEventBus.on(DataEventType.PERFORMANCE_THRESHOLD_EXCEEDED, (event: any) => {
      console.warn('Performance threshold exceeded:', event.data);
    });
  } catch (e) {
    console.warn('Failed to set up event listeners:', e);
  }
  
  // Add global hooks
  try {
    dataOperationHooks.addMetricsHook((context: any) => {
      if (context.phase === 'post' && context.duration) {
        console.log(`Operation ${context.operation} took ${context.duration}ms`);
      }
    });
  } catch (e) {
    console.warn('Failed to add hooks:', e);
  }
  
  console.log('Data integration system initialized');
};