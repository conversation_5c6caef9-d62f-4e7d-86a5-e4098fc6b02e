import { z, ZodSchema, ZodError } from 'zod';
import { dataEventBus, DataEventType } from '../EventBus';

export interface ValidationResult {
  valid: boolean;
  errors?: ValidationError[];
  warnings?: ValidationWarning[];
  metadata?: Record<string, any>;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

export interface BusinessRule {
  id: string;
  name: string;
  description?: string;
  condition: (data: any) => boolean | Promise<boolean>;
  errorMessage: string;
  severity: 'error' | 'warning';
}

export interface QualityMetric {
  id: string;
  name: string;
  calculate: (data: any) => number;
  threshold: number;
  operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq';
}

export interface QualityReport {
  overall: number;
  metrics: Array<{
    metric: QualityMetric;
    value: number;
    passed: boolean;
  }>;
  recommendations: string[];
}

export class DataValidator {
  private schemas: Map<string, ZodSchema> = new Map();
  private businessRules: Map<string, BusinessRule[]> = new Map();
  private qualityMetrics: Map<string, QualityMetric[]> = new Map();
  
  // Register a schema for a specific data type
  registerSchema(dataType: string, schema: ZodSchema): void {
    this.schemas.set(dataType, schema);
  }
  
  // Register business rules for a data type
  registerBusinessRules(dataType: string, rules: BusinessRule[]): void {
    this.businessRules.set(dataType, rules);
  }
  
  // Register quality metrics for a data type
  registerQualityMetrics(dataType: string, metrics: QualityMetric[]): void {
    this.qualityMetrics.set(dataType, metrics);
  }
  
  // Validate data against a schema
  async validateSchema(data: any, dataType: string): Promise<ValidationResult> {
    dataEventBus.emit(DataEventType.DATA_VALIDATION_STARTED, {
      dataType,
      validationType: 'schema'
    });
    
    const schema = this.schemas.get(dataType);
    if (!schema) {
      return {
        valid: false,
        errors: [{
          field: 'schema',
          message: `No schema registered for data type: ${dataType}`,
          code: 'SCHEMA_NOT_FOUND'
        }]
      };
    }
    
    try {
      await schema.parseAsync(data);
      
      dataEventBus.emit(DataEventType.DATA_VALIDATION_PASSED, {
        dataType,
        validationType: 'schema'
      });
      
      return { valid: true };
    } catch (error) {
      if (error instanceof ZodError) {
        const errors: ValidationError[] = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
          value: this.getValueAtPath(data, err.path)
        }));
        
        dataEventBus.emit(DataEventType.DATA_VALIDATION_FAILED, {
          dataType,
          validationType: 'schema',
          errors
        });
        
        return { valid: false, errors };
      }
      
      throw error;
    }
  }
  
  // Validate business rules
  async validateBusinessRules(data: any, dataType: string): Promise<ValidationResult> {
    dataEventBus.emit(DataEventType.DATA_VALIDATION_STARTED, {
      dataType,
      validationType: 'business_rules'
    });
    
    const rules = this.businessRules.get(dataType) || [];
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    for (const rule of rules) {
      try {
        const passed = await rule.condition(data);
        
        if (!passed) {
          if (rule.severity === 'error') {
            errors.push({
              field: rule.id,
              message: rule.errorMessage,
              code: `BUSINESS_RULE_${rule.id.toUpperCase()}`
            });
          } else {
            warnings.push({
              field: rule.id,
              message: rule.errorMessage
            });
          }
        }
      } catch (error) {
        errors.push({
          field: rule.id,
          message: `Error evaluating rule ${rule.name}: ${error}`,
          code: 'RULE_EVALUATION_ERROR'
        });
      }
    }
    
    const valid = errors.length === 0;
    
    if (valid) {
      dataEventBus.emit(DataEventType.DATA_VALIDATION_PASSED, {
        dataType,
        validationType: 'business_rules',
        warnings
      });
    } else {
      dataEventBus.emit(DataEventType.DATA_VALIDATION_FAILED, {
        dataType,
        validationType: 'business_rules',
        errors,
        warnings
      });
    }
    
    return { valid, errors, warnings };
  }
  
  // Validate data quality
  async validateDataQuality(data: any, dataType: string): Promise<QualityReport> {
    const metrics = this.qualityMetrics.get(dataType) || [];
    const results: QualityReport['metrics'] = [];
    const recommendations: string[] = [];
    
    for (const metric of metrics) {
      const value = metric.calculate(data);
      const passed = this.evaluateMetric(value, metric.threshold, metric.operator);
      
      results.push({ metric, value, passed });
      
      if (!passed) {
        recommendations.push(
          `${metric.name} is ${value}, expected ${metric.operator} ${metric.threshold}`
        );
      }
    }
    
    const overall = results.length > 0
      ? results.filter(r => r.passed).length / results.length
      : 1;
    
    return { overall, metrics: results, recommendations };
  }
  
  // Comprehensive validation
  async validate(data: any, dataType: string): Promise<{
    schema: ValidationResult;
    businessRules: ValidationResult;
    quality: QualityReport;
    overallValid: boolean;
  }> {
    const [schema, businessRules, quality] = await Promise.all([
      this.validateSchema(data, dataType),
      this.validateBusinessRules(data, dataType),
      this.validateDataQuality(data, dataType)
    ]);
    
    const overallValid = schema.valid && businessRules.valid && quality.overall >= 0.8;
    
    return {
      schema,
      businessRules,
      quality,
      overallValid
    };
  }
  
  // Helper methods
  private getValueAtPath(data: any, path: (string | number)[]): any {
    return path.reduce((current, segment) => current?.[segment], data);
  }
  
  private evaluateMetric(
    value: number,
    threshold: number,
    operator: QualityMetric['operator']
  ): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'gte': return value >= threshold;
      case 'lt': return value < threshold;
      case 'lte': return value <= threshold;
      case 'eq': return value === threshold;
      default: return false;
    }
  }
}

// Singleton instance
export const dataValidator = new DataValidator();

// Common validation schemas
export const commonSchemas = {
  email: z.string().email(),
  url: z.string().url(),
  uuid: z.string().uuid(),
  date: z.string().datetime(),
  
  // Data integration specific
  dataSource: z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum(['database', 'api', 'file', 'stream']),
    config: z.record(z.any()),
    active: z.boolean()
  }),
  
  dataMapping: z.object({
    source: z.string(),
    target: z.string(),
    transform: z.string().optional(),
    required: z.boolean().default(true)
  })
};