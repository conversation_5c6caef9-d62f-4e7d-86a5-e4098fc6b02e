import { useState, useEffect, useCallback, useRef } from 'react';
import { ParallelAgentsManager } from '@/lib/ai-agents/ParallelAgentsManager';
import { Task, ExecutionContext } from '@/lib/ai-agents/types';

export interface UseParallelAgentsReturn {
  isProcessing: boolean;
  metrics: any;
  clusters: any[];
  executionPlans: any[];
  startParallelExecution: (tasks: Task[]) => Promise<string>;
  pauseExecution: (planId: string) => Promise<void>;
  resumeExecution: (planId: string) => Promise<void>;
  cancelExecution: (planId: string) => Promise<void>;
  getExecutionStatus: (planId: string) => any;
  autoScaleAgents: (targetUtilization?: number) => Promise<void>;
  rebalanceWorkload: () => Promise<void>;
  error: string | null;
}

export function useParallelAgents(): UseParallelAgentsReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [metrics, setMetrics] = useState({});
  const [clusters, setClusters] = useState([]);
  const [executionPlans, setExecutionPlans] = useState([]);
  const [error, setError] = useState<string | null>(null);
  const [manager] = useState(() => ParallelAgentsManager.getInstance());
  const isMounted = useRef(true);

  // Update metrics periodically
  useEffect(() => {
    const updateMetrics = () => {
      if (!isMounted.current) return;
      
      try {
        const currentMetrics = manager.getParallelExecutionMetrics();
        setMetrics(currentMetrics);
        setClusters(currentMetrics.clusters || []);
        setExecutionPlans(currentMetrics.executionPlans || []);
      } catch (err) {
        console.error('Failed to update metrics:', err);
        if (isMounted.current) {
          setError(err instanceof Error ? err.message : 'Failed to update metrics');
        }
      }
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);

    return () => {
      clearInterval(interval);
      isMounted.current = false;
    };
  }, [manager]);

  const startParallelExecution = useCallback(async (tasks: Task[]): Promise<string> => {
    setIsProcessing(true);
    setError(null);
    try {
      const planId = await manager.startParallelExecution(tasks);
      return planId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start parallel execution';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }, [manager]);

  const pauseExecution = useCallback(async (planId: string): Promise<void> => {
    setError(null);
    try {
      await manager.pauseExecution(planId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pause execution';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [manager]);

  const resumeExecution = useCallback(async (planId: string): Promise<void> => {
    setError(null);
    try {
      await manager.resumeExecution(planId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to resume execution';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [manager]);

  const cancelExecution = useCallback(async (planId: string): Promise<void> => {
    setError(null);
    try {
      await manager.cancelExecution(planId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel execution';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [manager]);

  const getExecutionStatus = useCallback((planId: string) => {
    try {
      return manager.getExecutionStatus(planId);
    } catch (err) {
      console.error('Failed to get execution status:', err);
      return null;
    }
  }, [manager]);

  const autoScaleAgents = useCallback(async (targetUtilization: number = 0.75): Promise<void> => {
    setError(null);
    try {
      await manager.autoScaleAgents(0.8, targetUtilization);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to auto-scale agents';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [manager]);

  const rebalanceWorkload = useCallback(async (): Promise<void> => {
    setError(null);
    try {
      await manager.rebalanceWorkload();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to rebalance workload';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [manager]);

  return {
    isProcessing,
    metrics,
    clusters,
    executionPlans,
    startParallelExecution,
    pauseExecution,
    resumeExecution,
    cancelExecution,
    getExecutionStatus,
    autoScaleAgents,
    rebalanceWorkload,
    error
  };
}