import { useState, useEffect, useRef } from 'react';
import { useUIStore } from '@/stores/uiStore';
import { api } from '@/lib/api';

type InitializationStatus = 'initializing' | 'ready' | 'error';

export function useAppInitializer() {
  const [status, setStatus] = useState<InitializationStatus>('initializing');
  const [error, setLocalError] = useState<string | null>(null);
  const { setLoading, setError: setGlobalError } = useUIStore();
  const isMounted = useRef(true);

  useEffect(() => {
    const initialize = async () => {
      try {
        setLoading(true);
        setLocalError(null);
        console.log("Starting app initialization...");
        
        // Perform all startup operations here
        try {
          // Only check Claude version if <PERSON><PERSON> is available
          if (typeof window !== 'undefined' && window.__TAURI__ !== undefined) {
            await api.checkClaudeVersion();
            console.log("Claude version check completed");
          } else {
            console.log("Running in browser mode - skipping Claude version check");
          }
        } catch (err) {
          console.warn("Claude version check failed, continuing anyway:", err);
          // Don't fail initialization just because <PERSON> check failed
        }
        
        if (isMounted.current) {
          setStatus('ready');
          console.log("App initialization complete");
        }
      } catch (err) {
        console.error("Application initialization failed:", err);
        if (isMounted.current) {
          const errorMessage = "Failed to initialize the application. Please try restarting.";
          setGlobalError(errorMessage);
          setLocalError(errorMessage);
          setStatus('error');
        }
      } finally {
        if (isMounted.current) {
          setLoading(false);
        }
      }
    };

    initialize();

    return () => {
      isMounted.current = false;
    };
  }, [setLoading, setGlobalError]);

  return { status, error };
}