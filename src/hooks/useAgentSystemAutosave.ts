import { useEffect, useRef, useCallback } from 'react';
import { useAgentSystemStore } from '@/stores/agentSystemStore';
import { toast } from 'sonner';

export function useAgentSystemAutosave() {
  const { hasUnsavedChanges, markAsSaved, lastSaved } = useAgentSystemStore();
  const saveInProgress = useRef(false);

  // Save on window unload (browser close/refresh)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && !saveInProgress.current) {
        // Perform synchronous save
        try {
          markAsSaved();
        } catch (error) {
          console.error('Save on unload failed:', error);
        }
        
        // Show warning if there are unsaved changes
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, markAsSaved]);

  // Debounced autosave
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const timeoutId = setTimeout(() => {
      if (!saveInProgress.current) {
        performManualSave();
      }
    }, 2000);

    return () => clearTimeout(timeoutId);
  }, [hasUnsavedChanges]);

  const performManualSave = useCallback(async () => {
    if (saveInProgress.current) return;

    saveInProgress.current = true;
    try {
      await markAsSaved();
      // Show subtle notification
      toast.success('Agent system saved', {
        duration: 2000,
        position: 'bottom-left',
      });
    } catch (error) {
      console.error('Save failed:', error);
      toast.error('Failed to save', {
        duration: 3000,
        position: 'bottom-left',
      });
    } finally {
      saveInProgress.current = false;
    }
  }, [markAsSaved]);

  // Return save status for UI indicators
  return {
    hasUnsavedChanges,
    lastSaved,
    timeSinceLastSave: Date.now() - lastSaved,
    performManualSave,
    isSaving: saveInProgress.current
  };
}