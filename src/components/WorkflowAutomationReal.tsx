import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { WorkflowExecution } from '@/lib/parallel-agents/types';
import {
  Workflow,
  Play,
  Pause,
  Plus,
  Trash2,
  Edit,
  Save,
  RefreshCw,
  Clock,
  GitBranch,
  <PERSON>,
  <PERSON>Text,
  <PERSON>ting<PERSON>,
  Zap
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  type: 'agent' | 'command' | 'condition' | 'parallel';
  name: string;
  config: {
    agentId?: string;
    command?: string;
    condition?: string;
    parallel?: string[];
  };
  onSuccess?: string;
  onFailure?: string;
}

interface WorkflowConfig {
  id: string;
  name: string;
  description: string;
  trigger: 'manual' | 'schedule' | 'webhook' | 'file-change';
  triggerConfig?: {
    schedule?: string;
    webhookUrl?: string;
    watchPath?: string;
  };
  steps: WorkflowStep[];
  variables: Record<string, string>;
  enabled: boolean;
  lastRun?: Date;
  lastStatus?: 'success' | 'failed' | 'running';
}

// Using WorkflowExecution from '@/lib/parallel-agents/types'
// Extended with logs for UI display
interface WorkflowExecutionWithLogs extends Omit<WorkflowExecution, 'stepResults'> {
  logs: Array<{
    timestamp: Date;
    step: string;
    message: string;
    type: 'info' | 'error' | 'warning';
  }>;
  stepResults: Record<string, any>; // Convert Map to Record for serialization
}

export function WorkflowAutomationReal() {
  const [workflows, setWorkflows] = useState<WorkflowConfig[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecutionWithLogs[]>([]);
  const [agents, setAgents] = useState<any[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState<WorkflowConfig | null>(null);
  const [activeTab, setActiveTab] = useState('workflows');
  const [runningExecutions, setRunningExecutions] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadWorkflows();
    loadAgents();
  }, []);

  useEffect(() => {
    if (runningExecutions.size > 0) {
      const interval = setInterval(updateExecutionStatuses, 2000);
      return () => clearInterval(interval);
    }
  }, [runningExecutions]);

  const loadWorkflows = async () => {
    try {
      // Mock workflow data since API method doesn't exist
      const mockWorkflows: WorkflowConfig[] = [];
      setWorkflows(mockWorkflows);
    } catch (error) {
      console.error('Failed to load workflows:', error);
      toast.error('Failed to load workflows');
    }
  };

  const loadAgents = async () => {
    try {
      const agentList = await api.listAgents();
      setAgents(agentList);
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const updateExecutionStatuses = async () => {
    try {
      // Mock execution data since API method doesn't exist
      const activeExecutions: WorkflowExecutionWithLogs[] = [];
      
      setExecutions(prev => {
        const updated = [...prev];
        activeExecutions.forEach((exec: any) => {
          const index = updated.findIndex(e => e.id === exec.id);
          if (index >= 0) {
            updated[index] = exec;
            if (exec.status !== 'running') {
              setRunningExecutions(prev => {
                const newSet = new Set(prev);
                newSet.delete(exec.id);
                return newSet;
              });
            }
          }
        });
        return updated;
      });
    } catch (error) {
      console.error('Failed to update execution statuses:', error);
    }
  };

  const createNewWorkflow = () => {
    const newWorkflow: WorkflowConfig = {
      id: `workflow-${Date.now()}`,
      name: 'New Workflow',
      description: '',
      trigger: 'manual',
      steps: [],
      variables: {},
      enabled: true
    };
    
    setEditingWorkflow(newWorkflow);
    setIsEditing(true);
  };

  const saveWorkflow = async () => {
    if (!editingWorkflow) return;
    
    try {
      // Mock workflow save since API methods don't exist
      if (workflows.find(w => w.id === editingWorkflow.id)) {
        // Update existing workflow
        console.log('Would update workflow:', editingWorkflow);
      } else {
        // Create new workflow
        console.log('Would create workflow:', editingWorkflow);
      }
      
      await loadWorkflows();
      setIsEditing(false);
      setEditingWorkflow(null);
      toast.success('Workflow saved successfully');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast.error('Failed to save workflow');
    }
  };

  const deleteWorkflow = async (workflowId: string) => {
    try {
      await api.deleteWorkflow(workflowId);
      await loadWorkflows();
      toast.success('Workflow deleted');
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      toast.error('Failed to delete workflow');
    }
  };

  const executeWorkflow = async (workflow: WorkflowConfig) => {
    try {
      const executionId = await api.executeWorkflow(workflow.id);
      
      const execution: WorkflowExecutionWithLogs = {
        id: executionId,
        workflowId: workflow.id,
        status: 'running',
        startTime: Date.now(),
        progress: {
          currentStep: undefined,
          completedSteps: [],
          totalSteps: workflow.steps.length,
          percentage: 0
        },
        outputs: {},
        variables: {},
        stepResults: {},
        logs: []
      };
      
      setExecutions(prev => [execution, ...prev]);
      setRunningExecutions(prev => new Set(prev).add(executionId));
      setActiveTab('executions');
      
      toast.success(`Started workflow: ${workflow.name}`);
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      toast.error('Failed to execute workflow');
    }
  };

  const stopExecution = async (executionId: string) => {
    try {
      await api.stopWorkflowExecution(executionId);
      
      setExecutions(prev => prev.map(exec => 
        exec.id === executionId 
          ? { ...exec, status: 'failed' as const, endTime: Date.now() }
          : exec
      ));
      
      setRunningExecutions(prev => {
        const newSet = new Set(prev);
        newSet.delete(executionId);
        return newSet;
      });
      
      toast.success('Workflow execution stopped');
    } catch (error) {
      console.error('Failed to stop execution:', error);
      toast.error('Failed to stop execution');
    }
  };

  const addStep = (type: WorkflowStep['type']) => {
    if (!editingWorkflow) return;
    
    const newStep: WorkflowStep = {
      id: `step-${Date.now()}`,
      type,
      name: `New ${type} step`,
      config: {}
    };
    
    setEditingWorkflow({
      ...editingWorkflow,
      steps: [...editingWorkflow.steps, newStep]
    });
  };

  const updateStep = (stepId: string, updates: Partial<WorkflowStep>) => {
    if (!editingWorkflow) return;
    
    setEditingWorkflow({
      ...editingWorkflow,
      steps: editingWorkflow.steps.map(step => 
        step.id === stepId ? { ...step, ...updates } : step
      )
    });
  };

  const removeStep = (stepId: string) => {
    if (!editingWorkflow) return;
    
    setEditingWorkflow({
      ...editingWorkflow,
      steps: editingWorkflow.steps.filter(step => step.id !== stepId)
    });
  };

  const getStepIcon = (type: WorkflowStep['type']) => {
    switch (type) {
      case 'agent': return <Code className="w-4 h-4" />;
      case 'command': return <Settings className="w-4 h-4" />;
      case 'condition': return <GitBranch className="w-4 h-4" />;
      case 'parallel': return <Zap className="w-4 h-4" />;
    }
  };

  const getTriggerIcon = (trigger: WorkflowConfig['trigger']) => {
    switch (trigger) {
      case 'manual': return <Play className="w-4 h-4" />;
      case 'schedule': return <Clock className="w-4 h-4" />;
      case 'webhook': return <Workflow className="w-4 h-4" />;
      case 'file-change': return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Workflow Automation</h2>
            <p className="text-muted-foreground">
              Automate development tasks with custom workflows
            </p>
          </div>
          <Button onClick={createNewWorkflow}>
            <Plus className="mr-2 h-4 w-4" />
            New Workflow
          </Button>
        </div>
      </div>

      <div className="flex-1 p-6 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="executions">Executions</TabsTrigger>
            <TabsTrigger value="editor" disabled={!isEditing}>Editor</TabsTrigger>
          </TabsList>

          <TabsContent value="workflows" className="space-y-4">
            {workflows.length === 0 ? (
              <Alert>
                <AlertDescription>
                  No workflows created yet. Click "New Workflow" to get started.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {workflows.map(workflow => (
                  <Card key={workflow.id}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg">{workflow.name}</CardTitle>
                          <p className="text-sm text-muted-foreground mt-1">
                            {workflow.description}
                          </p>
                        </div>
                        <Badge variant={workflow.enabled ? 'default' : 'secondary'}>
                          {workflow.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-sm">
                          {getTriggerIcon(workflow.trigger)}
                          <span className="capitalize">{workflow.trigger} trigger</span>
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          {workflow.steps.length} steps
                        </div>
                        
                        {workflow.lastRun && (
                          <div className="flex items-center gap-2 text-sm">
                            <Clock className="w-3 h-3" />
                            Last run: {new Date(workflow.lastRun).toLocaleString()}
                            {workflow.lastStatus && (
                              <Badge variant={workflow.lastStatus === 'success' ? 'default' : 'destructive'}>
                                {workflow.lastStatus}
                              </Badge>
                            )}
                          </div>
                        )}
                        
                        <div className="flex gap-2 pt-2">
                          <Button
                            size="sm"
                            onClick={() => executeWorkflow(workflow)}
                            disabled={!workflow.enabled || workflow.steps.length === 0}
                          >
                            <Play className="mr-1 h-3 w-3" />
                            Run
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingWorkflow(workflow);
                              setIsEditing(true);
                              setActiveTab('editor');
                            }}
                          >
                            <Edit className="mr-1 h-3 w-3" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteWorkflow(workflow.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="executions" className="space-y-4">
            {executions.length === 0 ? (
              <Alert>
                <AlertDescription>
                  No workflow executions yet. Run a workflow to see its execution history.
                </AlertDescription>
              </Alert>
            ) : (
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  {executions.map(execution => {
                    const workflow = workflows.find(w => w.id === execution.workflowId);
                    return (
                      <Card key={execution.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-lg">
                                {workflow?.name || 'Unknown Workflow'}
                              </CardTitle>
                              <p className="text-sm text-muted-foreground">
                                Started: {new Date(execution.startTime).toLocaleString()}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={
                                execution.status === 'completed' ? 'default' :
                                execution.status === 'failed' ? 'destructive' : 'secondary'
                              }>
                                {execution.status === 'running' && (
                                  <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                                )}
                                {execution.status}
                              </Badge>
                              {execution.status === 'running' && (
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => stopExecution(execution.id)}
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {execution.progress.currentStep && (
                            <div className="mb-3">
                              <p className="text-sm text-muted-foreground">
                                Current step: {execution.progress.currentStep}
                              </p>
                            </div>
                          )}
                          
                          <div className="space-y-2">
                            <p className="text-sm font-medium">Recent logs:</p>
                            <div className="space-y-1">
                              {execution.logs.slice(-5).map((log, idx) => (
                                <div key={idx} className="text-xs font-mono">
                                  <span className="text-muted-foreground">
                                    [{log.timestamp.toLocaleTimeString()}]
                                  </span>
                                  <span className={
                                    log.type === 'error' ? 'text-red-600 ml-2' :
                                    log.type === 'warning' ? 'text-yellow-600 ml-2' : 'ml-2'
                                  }>
                                    {log.message}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </ScrollArea>
            )}
          </TabsContent>

          <TabsContent value="editor" className="space-y-4">
            {editingWorkflow && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Name</Label>
                        <Input
                          value={editingWorkflow.name}
                          onChange={(e) => setEditingWorkflow({
                            ...editingWorkflow,
                            name: e.target.value
                          })}
                        />
                      </div>
                      <div>
                        <Label>Trigger</Label>
                        <Select
                          value={editingWorkflow.trigger}
                          onValueChange={(value) => setEditingWorkflow({
                            ...editingWorkflow,
                            trigger: value as WorkflowConfig['trigger']
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="manual">Manual</SelectItem>
                            <SelectItem value="schedule">Schedule</SelectItem>
                            <SelectItem value="webhook">Webhook</SelectItem>
                            <SelectItem value="file-change">File Change</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div>
                      <Label>Description</Label>
                      <Textarea
                        value={editingWorkflow.description}
                        onChange={(e) => setEditingWorkflow({
                          ...editingWorkflow,
                          description: e.target.value
                        })}
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Workflow Steps</CardTitle>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => addStep('agent')}>
                          <Plus className="mr-1 h-3 w-3" />
                          Agent
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => addStep('command')}>
                          <Plus className="mr-1 h-3 w-3" />
                          Command
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => addStep('condition')}>
                          <Plus className="mr-1 h-3 w-3" />
                          Condition
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {editingWorkflow.steps.length === 0 ? (
                      <Alert>
                        <AlertDescription>
                          No steps added yet. Add steps to define your workflow.
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <div className="space-y-3">
                        {editingWorkflow.steps.map((step, index) => (
                          <div key={step.id} className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                {getStepIcon(step.type)}
                                <Input
                                  value={step.name}
                                  onChange={(e) => updateStep(step.id, { name: e.target.value })}
                                  className="w-64"
                                />
                              </div>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => removeStep(step.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                            
                            {step.type === 'agent' && (
                              <div>
                                <Label>Select Agent</Label>
                                <Select
                                  value={step.config.agentId}
                                  onValueChange={(value) => updateStep(step.id, {
                                    config: { ...step.config, agentId: value }
                                  })}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Choose an agent..." />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {agents.map(agent => (
                                      <SelectItem key={agent.id} value={agent.id}>
                                        {agent.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                            
                            {step.type === 'command' && (
                              <div>
                                <Label>Command</Label>
                                <Input
                                  value={step.config.command || ''}
                                  onChange={(e) => updateStep(step.id, {
                                    config: { ...step.config, command: e.target.value }
                                  })}
                                  placeholder="Enter command to execute..."
                                />
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <div className="flex gap-2">
                  <Button onClick={saveWorkflow}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Workflow
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditing(false);
                      setEditingWorkflow(null);
                      setActiveTab('workflows');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}