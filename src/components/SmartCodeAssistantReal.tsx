import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import {
  Code,
  RefreshCw,
  Search,
  Sparkles,
  TestTube,
  Zap,
  AlertCircle,
  FileText,
  Shield
} from 'lucide-react';

interface CodeSuggestion {
  id: string;
  type: 'refactor' | 'optimization' | 'bug-fix' | 'security' | 'test';
  title: string;
  description: string;
  code?: string;
  impact: 'high' | 'medium' | 'low';
  file?: string;
  line?: number;
}

interface CodeAnalysis {
  complexity: number;
  maintainability: number;
  coverage: number;
  issues: {
    bugs: number;
    vulnerabilities: number;
    codeSmells: number;
  };
}

export function SmartCodeAssistantReal() {
  const [activeProject, setActiveProject] = useState<string>('');
  const [codeInput, setCodeInput] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [suggestions, setSuggestions] = useState<CodeSuggestion[]>([]);
  const [codeAnalysis, setCodeAnalysis] = useState<CodeAnalysis | null>(null);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [recentFiles, setRecentFiles] = useState<string[]>([]);
  const [agents, setAgents] = useState<any[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCode, setGeneratedCode] = useState('');

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load agents
      const agentList = await api.listAgents();
      setAgents(agentList);
      
      // Select first code-related agent
      const codeAgent = agentList.find((a: any) =>
        a.name.toLowerCase().includes('code') ||
        a.name.toLowerCase().includes('developer')
      );
      if (codeAgent && codeAgent.id) {
        setSelectedAgent(codeAgent.id.toString());
      }

      // Load recent files - using mock data since API method doesn't exist
      const mockFiles = [
        'src/components/App.tsx',
        'src/lib/api.ts',
        'src/components/Settings.tsx',
        'package.json',
        'README.md'
      ];
      setRecentFiles(mockFiles);
      
      // Get current project
      const projects = await api.listProjects();
      if (projects.length > 0) {
        setActiveProject(projects[0].path);
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  };

  const analyzeCode = async () => {
    if (!codeInput.trim() && !selectedFile) {
      toast.error('Please enter code or select a file to analyze');
      return;
    }

    setIsAnalyzing(true);
    setSuggestions([]);
    
    try {
      let codeToAnalyze = codeInput;
      
      // If a file is selected, read its content
      if (selectedFile && !codeInput) {
        // Mock file reading since API method doesn't exist
        const mockContent = `// Mock content for ${selectedFile}\nfunction example() {\n  console.log("This is mock content");\n}`;
        codeToAnalyze = mockContent;
        setCodeInput(mockContent);
      }

      // Run code review with an agent
      if (selectedAgent) {
        const reviewPrompt = `Analyze the following code and provide:
1. Code quality assessment
2. Potential bugs or issues
3. Security vulnerabilities
4. Performance optimization opportunities
5. Refactoring suggestions
6. Test coverage recommendations

Code:
\`\`\`
${codeToAnalyze}
\`\`\`

Provide structured suggestions with clear titles and descriptions.`;

        // Execute agent and get run ID
        await api.executeAgent(
          parseInt(selectedAgent),
          activeProject || '.',
          reviewPrompt
        );

        // Mock analysis output for now
        const mockAnalysisOutput = `1. Code Quality: The code is well-structured but could benefit from type annotations.
2. Security: Consider validating user inputs to prevent XSS attacks.
3. Performance: The function could be optimized by memoizing expensive calculations.`;

        // Parse the analysis output into suggestions
        const parsedSuggestions = parseAnalysisOutput(mockAnalysisOutput);
        setSuggestions(parsedSuggestions);

        // Mock code analysis metrics (would need real static analysis tools)
        setCodeAnalysis({
          complexity: Math.floor(Math.random() * 100),
          maintainability: Math.floor(Math.random() * 100),
          coverage: Math.floor(Math.random() * 100),
          issues: {
            bugs: parsedSuggestions.filter(s => s.type === 'bug-fix').length,
            vulnerabilities: parsedSuggestions.filter(s => s.type === 'security').length,
            codeSmells: parsedSuggestions.filter(s => s.type === 'refactor').length
          }
        });
      }

      toast.success('Code analysis completed');
    } catch (error) {
      console.error('Failed to analyze code:', error);
      toast.error('Failed to analyze code');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const parseAnalysisOutput = (output: string): CodeSuggestion[] => {
    // Simple parsing - in production, you'd want more sophisticated parsing
    const suggestions: CodeSuggestion[] = [];
    const lines = output.split('\n');
    
    let currentSuggestion: Partial<CodeSuggestion> | null = null;
    let suggestionId = 0;

    for (const line of lines) {
      if (line.match(/^\d+\.|^-|^\*/)) {
        if (currentSuggestion && currentSuggestion.title) {
          suggestions.push({
            id: `suggestion-${suggestionId++}`,
            type: inferSuggestionType(currentSuggestion.title || ''),
            impact: inferImpact(currentSuggestion.description || ''),
            ...currentSuggestion
          } as CodeSuggestion);
        }
        
        currentSuggestion = {
          title: line.replace(/^\d+\.|-|\*/, '').trim(),
          description: ''
        };
      } else if (currentSuggestion && line.trim()) {
        currentSuggestion.description += line + '\n';
      }
    }

    // Add the last suggestion
    if (currentSuggestion && currentSuggestion.title) {
      suggestions.push({
        id: `suggestion-${suggestionId}`,
        type: inferSuggestionType(currentSuggestion.title || ''),
        impact: inferImpact(currentSuggestion.description || ''),
        ...currentSuggestion
      } as CodeSuggestion);
    }

    return suggestions;
  };

  const inferSuggestionType = (title: string): CodeSuggestion['type'] => {
    const lower = title.toLowerCase();
    if (lower.includes('bug') || lower.includes('error') || lower.includes('fix')) return 'bug-fix';
    if (lower.includes('security') || lower.includes('vulnerability')) return 'security';
    if (lower.includes('test') || lower.includes('coverage')) return 'test';
    if (lower.includes('performance') || lower.includes('optimize')) return 'optimization';
    return 'refactor';
  };

  const inferImpact = (description: string): CodeSuggestion['impact'] => {
    const lower = description.toLowerCase();
    if (lower.includes('critical') || lower.includes('security') || lower.includes('vulnerability')) return 'high';
    if (lower.includes('important') || lower.includes('should')) return 'medium';
    return 'low';
  };

  const generateCode = async (prompt: string) => {
    if (!selectedAgent) {
      toast.error('Please select an agent');
      return;
    }

    setIsGenerating(true);
    setGeneratedCode('');
    
    try {
      await api.executeAgent(
        parseInt(selectedAgent),
        activeProject || '.',
        prompt
      );

      // Mock the generated code
      const mockGeneratedCode = `// Generated code based on prompt: ${prompt}
function generatedFunction() {
  // Implementation here
  return "Mock generated code";
}`;
      
      setGeneratedCode(mockGeneratedCode);
      toast.success('Code generated successfully');
    } catch (error) {
      console.error('Failed to generate code:', error);
      toast.error('Failed to generate code');
    } finally {
      setIsGenerating(false);
    }
  };

  const applySuggestion = async (suggestion: CodeSuggestion) => {
    if (!suggestion.code) {
      // Generate implementation for the suggestion
      const prompt = `Implement the following suggestion:
Title: ${suggestion.title}
Description: ${suggestion.description}
Type: ${suggestion.type}

Provide only the code implementation without explanations.`;

      await generateCode(prompt);
    } else {
      setGeneratedCode(suggestion.code);
    }
  };

  const getSuggestionIcon = (type: CodeSuggestion['type']) => {
    switch (type) {
      case 'bug-fix': return <AlertCircle className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      case 'test': return <TestTube className="w-4 h-4" />;
      case 'optimization': return <Zap className="w-4 h-4" />;
      case 'refactor': return <RefreshCw className="w-4 h-4" />;
    }
  };

  const getImpactColor = (impact: CodeSuggestion['impact']) => {
    switch (impact) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b">
        <h2 className="text-2xl font-bold mb-2">Smart Code Assistant</h2>
        <p className="text-muted-foreground">
          AI-powered code analysis, suggestions, and generation
        </p>
      </div>

      <div className="flex-1 p-6 overflow-auto">
        <Tabs defaultValue="analyze" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="analyze">Code Analysis</TabsTrigger>
            <TabsTrigger value="generate">Code Generation</TabsTrigger>
            <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          </TabsList>

          <TabsContent value="analyze" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Code Input</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <label className="text-sm text-muted-foreground">Select Agent</label>
                    <select
                      className="w-full mt-1 p-2 border rounded"
                      value={selectedAgent}
                      onChange={(e) => setSelectedAgent(e.target.value)}
                    >
                      <option value="">Select an agent...</option>
                      {agents.map(agent => (
                        <option key={agent.id} value={agent.id}>
                          {agent.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex-1">
                    <label className="text-sm text-muted-foreground">Or select a file</label>
                    <select
                      className="w-full mt-1 p-2 border rounded"
                      value={selectedFile}
                      onChange={(e) => setSelectedFile(e.target.value)}
                    >
                      <option value="">Select a file...</option>
                      {recentFiles.map(file => (
                        <option key={file} value={file}>
                          {file}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <Textarea
                  placeholder="Paste your code here for analysis..."
                  value={codeInput}
                  onChange={(e) => setCodeInput(e.target.value)}
                  rows={12}
                  className="font-mono text-sm"
                />

                <Button
                  onClick={analyzeCode}
                  disabled={isAnalyzing}
                  className="w-full"
                >
                  {isAnalyzing ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Analyze Code
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {codeAnalysis && (
              <Card>
                <CardHeader>
                  <CardTitle>Code Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{codeAnalysis.complexity}</div>
                      <p className="text-sm text-muted-foreground">Complexity</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{codeAnalysis.maintainability}</div>
                      <p className="text-sm text-muted-foreground">Maintainability</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{codeAnalysis.coverage}%</div>
                      <p className="text-sm text-muted-foreground">Coverage</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {codeAnalysis.issues.bugs + codeAnalysis.issues.vulnerabilities}
                      </div>
                      <p className="text-sm text-muted-foreground">Issues</p>
                    </div>
                  </div>

                  <div className="mt-6 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Code Quality</span>
                      <span className="text-sm font-medium">
                        {codeAnalysis.maintainability > 80 ? 'Excellent' :
                         codeAnalysis.maintainability > 60 ? 'Good' :
                         codeAnalysis.maintainability > 40 ? 'Fair' : 'Poor'}
                      </span>
                    </div>
                    <Progress value={codeAnalysis.maintainability} />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="generate" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Code Generation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Generate unit tests for the analyzed code')}
                    disabled={isGenerating}
                  >
                    <TestTube className="mr-2 h-4 w-4" />
                    Unit Tests
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Generate documentation for the analyzed code')}
                    disabled={isGenerating}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Documentation
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Refactor the code for better performance')}
                    disabled={isGenerating}
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    Optimize
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Add error handling to the code')}
                    disabled={isGenerating}
                  >
                    <AlertCircle className="mr-2 h-4 w-4" />
                    Error Handling
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Add TypeScript types to the code')}
                    disabled={isGenerating}
                  >
                    <Code className="mr-2 h-4 w-4" />
                    TypeScript
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => generateCode('Add security best practices')}
                    disabled={isGenerating}
                  >
                    <Shield className="mr-2 h-4 w-4" />
                    Security
                  </Button>
                </div>

                <div>
                  <label className="text-sm text-muted-foreground">Custom Prompt</label>
                  <Textarea
                    placeholder="Describe what code you want to generate..."
                    className="mt-2"
                    rows={3}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && e.ctrlKey) {
                        generateCode(e.currentTarget.value);
                      }
                    }}
                  />
                </div>

                {generatedCode && (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="text-sm text-muted-foreground">Generated Code</label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(generatedCode);
                          toast.success('Code copied to clipboard');
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <ScrollArea className="h-[300px] border rounded">
                      <pre className="p-4 text-sm">
                        <code>{generatedCode}</code>
                      </pre>
                    </ScrollArea>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suggestions" className="space-y-4">
            {suggestions.length === 0 ? (
              <Alert>
                <AlertDescription>
                  No suggestions available. Analyze some code first.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {suggestions.map(suggestion => (
                  <Card key={suggestion.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getSuggestionIcon(suggestion.type)}
                          <h4 className="font-medium">{suggestion.title}</h4>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={getImpactColor(suggestion.impact)}>
                            {suggestion.impact} impact
                          </Badge>
                          <Badge variant="outline">{suggestion.type}</Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {suggestion.description}
                      </p>
                      
                      {suggestion.file && (
                        <p className="text-sm text-muted-foreground mb-3">
                          File: {suggestion.file}:{suggestion.line}
                        </p>
                      )}
                      
                      <Button
                        size="sm"
                        onClick={() => applySuggestion(suggestion)}
                      >
                        <Sparkles className="mr-2 h-4 w-4" />
                        Apply Suggestion
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}