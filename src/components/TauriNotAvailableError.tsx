import React from "react";
import { motion } from "framer-motion";
import { AlertTriangle, Terminal, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface TauriNotAvailableErrorProps {
  onBack?: () => void;
  feature?: string;
}

export const TauriNotAvailableError: React.FC<TauriNotAvailableErrorProps> = ({ 
  onBack, 
  feature = "this feature" 
}) => {
  return (
    <div className="flex items-center justify-center min-h-[400px] p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="w-full max-w-md"
      >
        <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/20">
              <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
            </div>
            <CardTitle className="text-amber-800 dark:text-amber-200">
              Tauri Application Required
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-sm text-amber-700 dark:text-amber-300">
              {feature} requires the Tauri application to be running. Please use one of the following methods:
            </p>
            
            <div className="space-y-3 text-left">
              <div className="rounded-lg bg-amber-100 dark:bg-amber-900/20 p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Terminal className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    Development Mode
                  </span>
                </div>
                <code className="text-xs text-amber-700 dark:text-amber-300 bg-amber-200 dark:bg-amber-800/30 px-2 py-1 rounded">
                  npm run tauri dev
                </code>
              </div>
              
              <div className="rounded-lg bg-amber-100 dark:bg-amber-900/20 p-3">
                <div className="flex items-center gap-2 mb-2">
                  <ExternalLink className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    Built Application
                  </span>
                </div>
                <p className="text-xs text-amber-700 dark:text-amber-300">
                  Use the compiled Tauri application instead of the web version
                </p>
              </div>
            </div>
            
            <div className="pt-4">
              {onBack && (
                <Button 
                  variant="outline" 
                  onClick={onBack}
                  className="w-full border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20"
                >
                  Go Back
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};
