import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Code,
  Gauge,
  Sparkles,
  Shield,
  Settings,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TooltipProvider } from '@/components/ui/tooltip';

interface SessionMode {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  features: string[];
  mcpServers: string[];
  settings: {
    tokenOptimization: boolean;
    introspection: boolean;
    thinkingDepth: 'minimal' | 'standard' | 'deep' | 'exhaustive';
  };
}

const SESSION_MODES: SessionMode[] = [
  {
    id: 'development',
    name: 'Development Focus',
    description: 'Optimized for coding with essential development tools',
    icon: Code,
    color: 'bg-blue-500',
    features: ['codeFlow', 'performanceProfiler', 'smartTemplates'],
    mcpServers: ['filesystem', 'git', 'github', 'memory'],
    settings: {
      tokenOptimization: true,
      introspection: false,
      thinkingDepth: 'standard',
    },
  },
  {
    id: 'analysis',
    name: 'Data Analysis',
    description: 'Perfect for data analysis and research tasks',
    icon: Gauge,
    color: 'bg-green-500',
    features: ['performanceProfiler', 'mcpMarketplace'],
    mcpServers: ['brave-search', 'sqlite', 'fetch', 'memory'],
    settings: {
      tokenOptimization: false,
      introspection: true,
      thinkingDepth: 'deep',
    },
  },
  {
    id: 'creative',
    name: 'Creative Mode',
    description: 'Enhanced for creative and experimental work',
    icon: Sparkles,
    color: 'bg-purple-500',
    features: ['voiceControl', 'collaboration', 'smartTemplates'],
    mcpServers: ['everart', 'memory', 'fetch', 'puppeteer'],
    settings: {
      tokenOptimization: false,
      introspection: false,
      thinkingDepth: 'exhaustive',
    },
  },
  {
    id: 'secure',
    name: 'Security Focused',
    description: 'Maximum security with minimal external connections',
    icon: Shield,
    color: 'bg-red-500',
    features: [],
    mcpServers: ['filesystem', 'memory'],
    settings: {
      tokenOptimization: true,
      introspection: false,
      thinkingDepth: 'minimal',
    },
  },
];

interface SessionModeSelectorProps {
  currentMode?: string;
  onModeSelect: (mode: SessionMode) => void;
  onCustomize: () => void;
  className?: string;
}

export const SessionModeSelector: React.FC<SessionModeSelectorProps> = ({
  currentMode = 'development',
  onModeSelect,
  onCustomize,
  className,
}) => {
  const [selectedMode, setSelectedMode] = useState(currentMode);

  const handleModeSelect = (mode: SessionMode) => {
    setSelectedMode(mode.id);
    onModeSelect(mode);
  };

  return (
    <TooltipProvider>
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Session Mode</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={onCustomize}
            className="flex items-center gap-1"
          >
            <Settings className="h-3 w-3" />
            Customize
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {SESSION_MODES.map((mode) => {
            const Icon = mode.icon;
            const isSelected = selectedMode === mode.id;

            return (
              <motion.div
                key={mode.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected ? 'ring-2 ring-primary shadow-md' : ''
                  }`}
                  onClick={() => handleModeSelect(mode)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${mode.color} text-white`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm">{mode.name}</h4>
                          {isSelected && (
                            <Badge variant="default" className="text-xs">
                              Active
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-xs text-muted-foreground mb-2">
                          {mode.description}
                        </p>
                        
                        <div className="space-y-2">
                          <div>
                            <span className="text-xs font-medium text-muted-foreground">Features:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {mode.features.slice(0, 3).map((feature) => (
                                <Badge key={feature} variant="secondary" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                              {mode.features.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{mode.features.length - 3}
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div>
                            <span className="text-xs font-medium text-muted-foreground">MCPs:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {mode.mcpServers.slice(0, 3).map((server) => (
                                <Badge key={server} variant="outline" className="text-xs">
                                  {server}
                                </Badge>
                              ))}
                              {mode.mcpServers.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{mode.mcpServers.length - 3}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Select a mode to automatically configure MCPs, features, and AI settings
          </p>
        </div>
      </div>
    </TooltipProvider>
  );
};