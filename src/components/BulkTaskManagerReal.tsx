import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import {
  Upload,
  Play,
  Pause,
  RotateCcw,
  Download,
  Brain,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Split,
  Users,
  FileUp,
  RefreshCw
} from 'lucide-react';

interface BulkTask {
  id: string;
  content: string;
  type: 'code-review' | 'refactor' | 'test' | 'document' | 'security' | 'optimize';
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'pending' | 'assigned' | 'running' | 'completed' | 'failed';
  assignedAgentId?: string;
  assignedAgentName?: string;
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  output?: string;
  error?: string;
  batchId: string;
}

interface TaskBatch {
  id: string;
  name: string;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  startedAt: Date;
  estimatedCompletion?: Date;
  status: 'preparing' | 'running' | 'completed' | 'failed';
}

export function BulkTaskManagerReal() {
  const [tasks, setTasks] = useState<BulkTask[]>([]);
  const [batches, setBatches] = useState<TaskBatch[]>([]);
  const [agents, setAgents] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('create');
  const [taskInput, setTaskInput] = useState('');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState<string | null>(null);
  const [metrics, setMetrics] = useState({
    totalProcessed: 0,
    successRate: 0,
    averageTime: 0,
    activeAgents: 0
  });

  useEffect(() => {
    loadAgents();
  }, []);

  useEffect(() => {
    if (isProcessing) {
      const interval = setInterval(updateTaskStatuses, 2000);
      return () => clearInterval(interval);
    }
  }, [isProcessing]);

  const loadAgents = async () => {
    try {
      const agentList = await api.listAgents();
      setAgents(agentList);
    } catch (error) {
      console.error('Failed to load agents:', error);
      toast.error('Failed to load agents');
    }
  };

  const updateTaskStatuses = async () => {
    try {
      // Get running sessions
      const sessions = await api.listRunningAgentSessions();
      
      // Update task statuses based on running sessions
      setTasks(prev => prev.map(task => {
        if (task.status === 'completed' || task.status === 'failed') return task;
        
        const session = sessions.find((s: any) => 
          s.process_type.AgentRun?.agent_id === task.assignedAgentId &&
          s.task.includes(task.id)
        );
        
        if (session) {
          return {
            ...task,
            status: 'running',
            progress: Math.min(90, task.progress + 10)
          };
        }
        
        return task;
      }));

      // Update metrics
      updateMetrics();
    } catch (error) {
      console.error('Failed to update task statuses:', error);
    }
  };

  const updateMetrics = () => {
    const completed = tasks.filter(t => t.status === 'completed').length;
    const failed = tasks.filter(t => t.status === 'failed').length;
    const total = tasks.length;
    
    const totalTime = tasks
      .filter(t => t.completedAt && t.startedAt)
      .reduce((acc, t) => acc + (t.completedAt!.getTime() - t.startedAt!.getTime()), 0);
    
    const activeAgents = new Set(
      tasks
        .filter(t => t.status === 'running' && t.assignedAgentId)
        .map(t => t.assignedAgentId)
    ).size;

    setMetrics({
      totalProcessed: completed + failed,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      averageTime: completed > 0 ? totalTime / completed / 1000 : 0,
      activeAgents
    });

    // Update batch statuses
    setBatches(prev => prev.map(batch => {
      const batchTasks = tasks.filter(t => t.batchId === batch.id);
      const completedInBatch = batchTasks.filter(t => t.status === 'completed').length;
      const failedInBatch = batchTasks.filter(t => t.status === 'failed').length;
      
      return {
        ...batch,
        completedTasks: completedInBatch,
        failedTasks: failedInBatch,
        status: completedInBatch + failedInBatch === batch.totalTasks 
          ? 'completed' 
          : 'running'
      };
    }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'text/csv') {
      setCsvFile(file);
      
      // Read CSV content
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setTaskInput(text);
      };
      reader.readAsText(file);
    } else {
      toast.error('Please upload a valid CSV file');
    }
  };

  const parseTasks = (input: string): Omit<BulkTask, 'id' | 'status' | 'progress' | 'batchId'>[] => {
    const lines = input.split('\n').filter(line => line.trim());
    
    return lines.map(line => {
      const [content, type = 'code-review', priority = 'medium'] = line.split(',').map(s => s.trim());
      
      return {
        content,
        type: type as BulkTask['type'],
        priority: priority as BulkTask['priority']
      };
    });
  };

  const startBulkProcessing = async () => {
    if (!taskInput.trim()) {
      toast.error('Please enter tasks or upload a CSV file');
      return;
    }

    if (agents.length === 0) {
      toast.error('No agents available. Please create agents first.');
      return;
    }

    setIsProcessing(true);
    
    // Create batch
    const batchId = `batch-${Date.now()}`;
    const batch: TaskBatch = {
      id: batchId,
      name: `Bulk Processing ${new Date().toLocaleDateString()}`,
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      startedAt: new Date(),
      status: 'preparing'
    };

    // Parse and create tasks
    const parsedTasks = parseTasks(taskInput);
    const newTasks: BulkTask[] = parsedTasks.map((task, index) => ({
      ...task,
      id: `task-${batchId}-${index}`,
      status: 'pending',
      progress: 0,
      batchId
    }));

    batch.totalTasks = newTasks.length;
    batch.estimatedCompletion = new Date(Date.now() + newTasks.length * 5000); // Estimate 5s per task

    setBatches(prev => [...prev, batch]);
    setTasks(prev => [...prev, ...newTasks]);
    setSelectedBatch(batchId);

    // Process tasks with agent assignment
    await processTasks(newTasks);
  };

  const processTasks = async (tasksToProcess: BulkTask[]) => {
    // Group tasks by type for efficient agent assignment
    const tasksByType = tasksToProcess.reduce((acc, task) => {
      if (!acc[task.type]) acc[task.type] = [];
      acc[task.type].push(task);
      return acc;
    }, {} as Record<string, BulkTask[]>);

    // Assign agents based on specialization
    const agentAssignments: { task: BulkTask; agent: any }[] = [];
    
    for (const [type, typeTasks] of Object.entries(tasksByType)) {
      // Find suitable agents for this task type
      const suitableAgents = agents.filter(agent => {
        const agentType = agent.name.toLowerCase();
        switch (type) {
          case 'code-review': return agentType.includes('review');
          case 'test': return agentType.includes('test');
          case 'document': return agentType.includes('doc');
          case 'security': return agentType.includes('security');
          default: return true;
        }
      });

      const agentsToUse = suitableAgents.length > 0 ? suitableAgents : agents;
      
      // Assign tasks round-robin to available agents
      typeTasks.forEach((task, index) => {
        const agent = agentsToUse[index % agentsToUse.length];
        agentAssignments.push({ task, agent });
        
        // Update task with assignment
        setTasks(prev => prev.map(t => 
          t.id === task.id 
            ? { 
                ...t, 
                status: 'assigned', 
                assignedAgentId: agent.id,
                assignedAgentName: agent.name 
              } 
            : t
        ));
      });
    }

    // Process assignments in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < agentAssignments.length; i += batchSize) {
      const batch = agentAssignments.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async ({ task, agent }, index) => {
          // Add delay to stagger starts
          await new Promise(resolve => setTimeout(resolve, index * 500));
          await executeTask(task, agent);
        })
      );
      
      // Wait between batches
      if (i + batchSize < agentAssignments.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    setIsProcessing(false);
  };

  const executeTask = async (task: BulkTask, agent: any) => {
    try {
      // Update task status
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { ...t, status: 'running', startedAt: new Date(), progress: 10 } 
          : t
      ));

      // Prepare task prompt based on type
      const taskPrompt = createTaskPrompt(task);
      
      // Execute agent
      const runId = await api.executeAgent(
        agent.id,
        taskPrompt,
        task.content,
        'sonnet'
      );
      
      // Update progress periodically while running
      const progressInterval = setInterval(() => {
        setTasks(prev => prev.map(t => 
          t.id === task.id && t.progress < 90
            ? { ...t, progress: Math.min(90, t.progress + 10) } 
            : t
        ));
      }, 1000);
      
      // Wait for completion (this is a simplified approach)
      await new Promise(resolve => setTimeout(resolve, 5000));
      clearInterval(progressInterval);
      
      const output = `Task ${task.id} processed by agent ${agent.name}`;

      // Mark as completed
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { 
              ...t, 
              status: 'completed', 
              progress: 100,
              completedAt: new Date(),
              output 
            } 
          : t
      ));

    } catch (error) {
      console.error(`Failed to execute task ${task.id}:`, error);
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { 
              ...t, 
              status: 'failed',
              completedAt: new Date(),
              error: error instanceof Error ? error.message : 'Unknown error'
            } 
          : t
      ));
    }
  };

  const createTaskPrompt = (task: BulkTask): string => {
    const prompts: Record<BulkTask['type'], string> = {
      'code-review': `Perform a code review for: ${task.content}`,
      'refactor': `Refactor the following code or component: ${task.content}`,
      'test': `Generate comprehensive tests for: ${task.content}`,
      'document': `Create documentation for: ${task.content}`,
      'security': `Perform security analysis on: ${task.content}`,
      'optimize': `Optimize the following: ${task.content}`
    };

    return prompts[task.type] || task.content;
  };

  const exportResults = () => {
    if (tasks.length === 0) {
      toast.error('No tasks to export');
      return;
    }

    const csv = [
      'ID,Content,Type,Priority,Status,Agent,Started,Completed,Output,Error',
      ...tasks.map(task => [
        task.id,
        `"${task.content}"`,
        task.type,
        task.priority,
        task.status,
        task.assignedAgentName || '',
        task.startedAt?.toISOString() || '',
        task.completedAt?.toISOString() || '',
        `"${(task.output || '').replace(/"/g, '""')}"`,
        `"${(task.error || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk-tasks-${selectedBatch || 'all'}.csv`;
    a.click();
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  const retryFailed = async () => {
    const failedTasks = tasks.filter(t => t.status === 'failed');
    if (failedTasks.length === 0) {
      toast.info('No failed tasks to retry');
      return;
    }

    // Reset failed tasks
    setTasks(prev => prev.map(t => 
      t.status === 'failed' 
        ? { ...t, status: 'pending', progress: 0, error: undefined } 
        : t
    ));

    setIsProcessing(true);
    await processTasks(failedTasks);
  };

  const getStatusColor = (status: BulkTask['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'running': return 'text-blue-600';
      case 'assigned': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: BulkTask['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'running': return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'assigned': return <Users className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b">
        <h2 className="text-2xl font-bold mb-2">Bulk Task Manager</h2>
        <p className="text-muted-foreground">
          Process thousands of tasks efficiently with AI agent delegation
        </p>
      </div>

      <div className="flex-1 p-6 overflow-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Tasks</p>
                  <p className="text-2xl font-bold">{tasks.length}</p>
                </div>
                <Split className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">
                    {metrics.successRate.toFixed(1)}%
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Avg Time</p>
                  <p className="text-2xl font-bold">
                    {metrics.averageTime.toFixed(1)}s
                  </p>
                </div>
                <Clock className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Agents</p>
                  <p className="text-2xl font-bold">{metrics.activeAgents}</p>
                </div>
                <Brain className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="create">Create Tasks</TabsTrigger>
            <TabsTrigger value="monitor">Monitor Progress</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Bulk Task Input</CardTitle>
                <CardDescription>
                  Enter tasks manually or upload a CSV file
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <label
                      htmlFor="csv-upload"
                      className="flex items-center justify-center px-4 py-2 border border-dashed rounded-lg cursor-pointer hover:bg-muted"
                    >
                      <FileUp className="mr-2 h-4 w-4" />
                      Upload CSV
                      <input
                        id="csv-upload"
                        type="file"
                        accept=".csv"
                        className="hidden"
                        onChange={handleFileUpload}
                      />
                    </label>
                    {csvFile && (
                      <p className="text-sm text-muted-foreground mt-2">
                        Loaded: {csvFile.name}
                      </p>
                    )}
                  </div>
                  <Button
                    onClick={startBulkProcessing}
                    disabled={isProcessing || !taskInput.trim()}
                  >
                    {isProcessing ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Processing
                      </>
                    )}
                  </Button>
                </div>

                <Textarea
                  placeholder="Enter tasks (one per line) or CSV format: content,type,priority
Example:
Review authentication module,code-review,high
Generate tests for user service,test,medium
Document API endpoints,document,low"
                  value={taskInput}
                  onChange={(e) => setTaskInput(e.target.value)}
                  rows={10}
                  className="font-mono text-sm"
                />

                {agents.length === 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      No agents available. Please create agents before processing tasks.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitor" className="space-y-4">
            {batches.length > 0 && (
              <Card className="mb-4">
                <CardHeader>
                  <CardTitle>Active Batches</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {batches.map(batch => (
                      <div
                        key={batch.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedBatch === batch.id ? 'border-primary' : ''
                        }`}
                        onClick={() => setSelectedBatch(batch.id)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{batch.name}</h4>
                          <Badge variant={batch.status === 'completed' ? 'secondary' : 'default'}>
                            {batch.status}
                          </Badge>
                        </div>
                        <Progress
                          value={(batch.completedTasks / batch.totalTasks) * 100}
                          className="h-2"
                        />
                        <div className="flex justify-between text-sm text-muted-foreground mt-2">
                          <span>{batch.completedTasks}/{batch.totalTasks} completed</span>
                          <span>{batch.failedTasks} failed</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Task Queue</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-2">
                    {tasks
                      .filter(task => !selectedBatch || task.batchId === selectedBatch)
                      .map(task => (
                        <motion.div
                          key={task.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="p-3 border rounded-lg"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(task.status)}
                              <span className={`font-medium ${getStatusColor(task.status)}`}>
                                {task.content}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{task.type}</Badge>
                              <Badge variant={
                                task.priority === 'critical' ? 'destructive' :
                                task.priority === 'high' ? 'default' :
                                'secondary'
                              }>
                                {task.priority}
                              </Badge>
                            </div>
                          </div>
                          
                          {task.status === 'running' && (
                            <Progress value={task.progress} className="h-2 mb-2" />
                          )}
                          
                          {task.assignedAgentName && (
                            <p className="text-sm text-muted-foreground">
                              Agent: {task.assignedAgentName}
                            </p>
                          )}
                          
                          {task.error && (
                            <p className="text-sm text-red-600 mt-2">{task.error}</p>
                          )}
                        </motion.div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Processing Results</h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={retryFailed}
                  disabled={isProcessing || tasks.filter(t => t.status === 'failed').length === 0}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Retry Failed
                </Button>
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={tasks.length === 0}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export CSV
                </Button>
              </div>
            </div>

            <Card>
              <CardContent className="p-0">
                <ScrollArea className="h-[500px]">
                  <div className="p-4 space-y-3">
                    {tasks
                      .filter(task => task.status === 'completed' || task.status === 'failed')
                      .filter(task => !selectedBatch || task.batchId === selectedBatch)
                      .map(task => (
                        <div key={task.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{task.content}</h4>
                            <Badge variant={task.status === 'completed' ? 'secondary' : 'destructive'}>
                              {task.status}
                            </Badge>
                          </div>
                          
                          <div className="text-sm text-muted-foreground mb-2">
                            <span>Agent: {task.assignedAgentName}</span>
                            {task.completedAt && task.startedAt && (
                              <span className="ml-4">
                                Time: {((task.completedAt.getTime() - task.startedAt.getTime()) / 1000).toFixed(1)}s
                              </span>
                            )}
                          </div>
                          
                          {task.output && (
                            <div className="p-3 bg-muted rounded text-sm font-mono overflow-x-auto">
                              {task.output}
                            </div>
                          )}
                          
                          {task.error && (
                            <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded text-sm text-red-600">
                              {task.error}
                            </div>
                          )}
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}