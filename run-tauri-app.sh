#!/bin/bash

# <PERSON><PERSON>t to run the Claudia app properly with Tauri

echo "Starting Claudia with Tauri..."

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed. Please install Node.js and npm first."
    exit 1
fi

# Check if Tauri CLI is installed
if ! npm list -g @tauri-apps/cli &> /dev/null; then
    echo "Installing Tauri CLI globally..."
    npm install -g @tauri-apps/cli
fi

# Run the app with Tauri
echo "Running 'npm run tauri dev'..."
npm run tauri dev

# If the command fails, provide troubleshooting steps
if [ $? -ne 0 ]; then
    echo "Failed to start Tauri app. Try these troubleshooting steps:"
    echo "1. Make sure you have Rust installed (rustc --version)"
    echo "2. Run 'npm install' to install dependencies"
    echo "3. Check for any build errors in the console"
    echo "4. Make sure you're in the correct directory"
fi
